#!/usr/bin/env node

const { exec } = require('child_process');
const path = require('path');

// Test firmware flashing process
const deviceUser = 'smartdockai';
const devicePassword = 'Sach@151084!';
const operatorUser = 'smartdockop';
const operatorPassword = 'Shiv@2703!';
const testIP = '*************';

// Create a test firmware file
const testFirmwareFile = '/tmp/test_firmware';
const targetFileName = 'svdgs_op';

async function execCommand(command, timeout = 15) {
    return new Promise((resolve, reject) => {
        console.log(`Executing: ${command}`);
        const process = exec(command);

        let output = '';
        const timeoutId = setTimeout(() => {
            process.kill();
            reject(new Error('Command timed out'));
        }, timeout * 1000);

        process.stdout.on('data', (data) => {
            output += data.toString();
        });

        process.stderr.on('data', (data) => {
            output += data.toString();
        });

        process.on('close', (code) => {
            clearTimeout(timeoutId);
            if (code === 0) {
                resolve({ output, code });
            } else {
                reject(new Error(`Command exited with code ${code}. Output: ${output}`));
            }
        });
    });
}

async function createTestFirmware() {
    try {
        await execCommand(`echo "Test firmware content" > ${testFirmwareFile}`);
        await execCommand(`chmod +x ${testFirmwareFile}`);
        console.log('✅ Test firmware file created');
        return true;
    } catch (error) {
        console.error('❌ Failed to create test firmware:', error.message);
        return false;
    }
}

async function testFirmwareCommands() {
    const commands = [
        {
            cmd: `sshpass -p '${devicePassword}' scp -o StrictHostKeyChecking=no "${testFirmwareFile}" ${deviceUser}@${testIP}:~/${targetFileName}`,
            desc: 'Copying operator firmware'
        },
        {
            cmd: `sshpass -p '${devicePassword}' ssh -o StrictHostKeyChecking=no ${deviceUser}@${testIP} 'ls -la ${targetFileName}'`,
            desc: 'Verifying firmware file copied'
        },
        {
            cmd: `sshpass -p '${devicePassword}' ssh -o StrictHostKeyChecking=no ${deviceUser}@${testIP} 'sshpass -p "${operatorPassword}" ssh -o StrictHostKeyChecking=no ${operatorUser}@*********** "echo \\"${operatorPassword}\\" | sudo -S systemctl status opserver.service"'`,
            desc: 'Checking opserver.service status'
        },
        {
            cmd: `sshpass -p '${devicePassword}' ssh -o StrictHostKeyChecking=no ${deviceUser}@${testIP} 'sshpass -p "${operatorPassword}" scp -o StrictHostKeyChecking=no ${targetFileName} ${operatorUser}@***********:~/'`,
            desc: 'Copying firmware to operator server'
        },
        {
            cmd: `sshpass -p '${devicePassword}' ssh -o StrictHostKeyChecking=no ${deviceUser}@${testIP} 'sshpass -p "${operatorPassword}" ssh -o StrictHostKeyChecking=no ${operatorUser}@*********** "ls -la ${targetFileName}"'`,
            desc: 'Verifying firmware on operator server'
        },
        {
            cmd: `sshpass -p '${devicePassword}' ssh -o StrictHostKeyChecking=no ${deviceUser}@${testIP} 'rm ${targetFileName}'`,
            desc: 'Cleaning up firmware file on smartdockai device'
        },
        {
            cmd: `sshpass -p '${devicePassword}' ssh -o StrictHostKeyChecking=no ${deviceUser}@${testIP} 'sshpass -p "${operatorPassword}" ssh -o StrictHostKeyChecking=no ${operatorUser}@*********** "rm ${targetFileName}"'`,
            desc: 'Cleaning up firmware file on operator server'
        }
    ];

    console.log('\n=== Testing Firmware Commands ===');
    
    for (const command of commands) {
        try {
            console.log(`\n🔄 ${command.desc}...`);
            const result = await execCommand(command.cmd, 30);
            console.log(`✅ Success: ${command.desc}`);
            if (result.output.trim()) {
                console.log(`Output: ${result.output.trim()}`);
            }
        } catch (error) {
            console.error(`❌ Failed: ${command.desc}`);
            console.error(`Error: ${error.message}`);
            return false;
        }
    }
    
    return true;
}

async function main() {
    console.log('=== Testing Firmware Flashing Process ===');
    
    // Create test firmware file
    const firmwareCreated = await createTestFirmware();
    if (!firmwareCreated) {
        return;
    }
    
    // Test firmware commands
    const success = await testFirmwareCommands();
    
    // Cleanup
    try {
        await execCommand(`rm -f ${testFirmwareFile}`);
        console.log('\n🧹 Cleaned up test firmware file');
    } catch (error) {
        console.error('Warning: Failed to cleanup test firmware file');
    }
    
    if (success) {
        console.log('\n🎉 All firmware commands successful!');
    } else {
        console.log('\n💥 Some firmware commands failed');
    }
}

main().catch(console.error);
