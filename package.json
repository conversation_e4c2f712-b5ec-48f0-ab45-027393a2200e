{"name": "svdgs-electron", "version": "1.0.0", "description": "SVDGS Desktop Application", "main": "main.js", "scripts": {"start": "electron .", "start:no-sandbox": "electron --no-sandbox .", "dist": "electron-builder"}, "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "homepage": "https://placeholder.svdgs.local", "devDependencies": {"electron": "^35.2.1", "electron-builder": "^24.13.3"}, "build": {"appId": "com.example.svdgs", "productName": "SVDGS Electron App", "files": ["main.js", "preload.js", "renderer/**/*", "index.html", "style.css", "package.json"], "win": {"target": {"target": "nsis", "arch": ["x64"]}}}}