#!/usr/bin/env node

const { exec } = require('child_process');

// Test SSH connection to the device
const deviceUser = 'smartdockai';
const devicePassword = 'Sach@151084!';
const operatorUser = 'smartdockop';
const operatorPassword = 'Shiv@2703!';
const testIP = '*************';

async function testConnection(ip, user, password, timeout = 3) {
    return new Promise((resolve) => {
        const testCmd = `timeout ${timeout} sshpass -p '${password}' ssh -o ConnectTimeout=${timeout} -o StrictHostKeyChecking=no ${user}@${ip} 'echo Connection successful'`;
        console.log(`Testing SSH connection to ${ip}...`);

        exec(testCmd, (error, stdout, stderr) => {
            if (error) {
                console.log(`SSH connection to ${ip} failed: ${error.message}`);
                resolve(false);
            } else {
                console.log(`SSH connection to ${ip} successful`);
                console.log(`Output: ${stdout.trim()}`);
                resolve(true);
            }
        });
    });
}

async function testOperatorConnection(ip) {
    return new Promise((resolve) => {
        const testCmd = `sshpass -p '${devicePassword}' ssh -o StrictHostKeyChecking=no ${deviceUser}@${ip} 'sshpass -p "${operatorPassword}" ssh -o StrictHostKeyChecking=no ${operatorUser}@*********** "echo \\"Operator connection successful\\""'`;
        console.log(`Testing operator connection via ${ip}...`);

        exec(testCmd, (error, stdout, stderr) => {
            if (error) {
                console.log(`Operator connection via ${ip} failed: ${error.message}`);
                console.log(`stderr: ${stderr}`);
                resolve(false);
            } else {
                console.log(`Operator connection via ${ip} successful`);
                console.log(`Output: ${stdout.trim()}`);
                resolve(true);
            }
        });
    });
}

async function main() {
    console.log('=== Testing SSH Connections ===');
    
    // Test direct connection to smartdockai device
    const deviceConnected = await testConnection(testIP, deviceUser, devicePassword);
    
    if (deviceConnected) {
        console.log('\n=== Testing Operator Connection ===');
        // Test connection to operator server via smartdockai device
        const operatorConnected = await testOperatorConnection(testIP);
        
        if (operatorConnected) {
            console.log('\n✅ All connections successful!');
        } else {
            console.log('\n❌ Operator connection failed');
        }
    } else {
        console.log('\n❌ Device connection failed');
    }
}

main().catch(console.error);
