<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no" />
    <title>Smart Docking AI Firmware Flasher</title>
    <link
      href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.5/dist/css/bootstrap.min.css"
      rel="stylesheet"
      integrity="sha384-SgOJa3DmI69IUzQ2PVdRZhwQ+dy64/BUtbMJw1MZ8t5HZApcHrRKUc4W0kG879m7"
      crossorigin="anonymous"
    />
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.1/font/bootstrap-icons.css">
    <link rel="stylesheet" href="style.css">
    <style>
      :root {
        --vs-code-bg: #1e1e1e;
        --vs-code-lighter-bg: #252526;
        --vs-code-darker-bg: #1a1a1a;
        --vs-code-text: #d4d4d4;
        --vs-code-border: #3e3e42;
      }

      body {
        /* Add padding to prevent content from being hidden behind fixed elements */
        padding-bottom: 350px;
        padding-top: 0;
        margin-top: 0;
        background-color: var(--vs-code-bg) !important;
        color: var(--vs-code-text);
      }

      /* Style for the fixed header */
      .heading-box {
        background-color: var(--vs-code-darker-bg);
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
        border-bottom: 1px solid var(--vs-code-border);
      }

      .heading-box h3 {
        font-weight: 600;
        color: #cccccc;
      }

      /* Styles for the log console */
      .log-console {
        transition: all 0.3s ease;
        max-height: 300px;
      }

      .log-console.collapsed {
        max-height: 40px !important;
        overflow: hidden;
      }

      /* Add a hover effect to the toggle button */
      #toggleOperationLog:hover {
        background-color: rgba(255, 255, 255, 0.2);
      }
    </style>
    <script
      src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.5/dist/js/bootstrap.bundle.min.js"
      integrity="sha384-k6d4wzSIapyDyv1kpU366/PK5hCdSbCRGRCMv+eplOQJWyd1fbcAu9OCUj5zNLiq"
      crossorigin="anonymous"
    ></script>

  </head>
  <body>
    <!-- Fixed Header at the top -->
    <div class="container-fluid fixed-top p-0 m-0" style="z-index: 1000; background-color: var(--vs-code-darker-bg); top: 0;">
      <div class="row m-0">
        <div class="col-12 py-2 text-center heading-box border-bottom">
          <h3 class="m-0 fs-6">Smart Docking AI Firmware Flasher</h3>
        </div>
      </div>
    </div>

    <!-- Spacer to prevent content from being hidden behind the fixed header -->
    <div style="height: 60px;"></div>

    <div class="container py-4" id="page">
    <div class="container mb-12 mt-3">
      <!-- Back Button and Log Button -->
      <div class="d-flex justify-content-between mb-4">
        <!-- <button id="showLogButton" class="btn" style="background-color: #252526; color: #cccccc; border: 1px solid #3e3e42;">
          <i class="bi bi-terminal me-2"></i>Show Log Console
        </button> -->
        <button id="backButton" class="btn" style="background-color: #252526; color: #cccccc; border: 1px solid #3e3e42;">
          <i class="bi bi-arrow-left me-2"></i>Home
        </button>
      </div>

      <!-- Selected Devices Table -->
      <div class="card mb-4" style="background-color: var(--vs-code-lighter-bg); border: 1px solid var(--vs-code-border); border-radius: 6px;">
        <div class="card-header" style="background-color: #2d2d2d; border-bottom: 1px solid var(--vs-code-border);">
          <h5 style="color: #cccccc;">Selected Devices</h5>
        </div>
        <div class="card-body">
          <div class="table-responsive">
            <table class="table table-hover" style="color: var(--vs-code-text); border-color: var(--vs-code-border);">
              <thead style="background-color: #2d2d2d; color: #cccccc;">
                <tr>
                  <th width="50">
                    <div class="form-check">
                      <input class="form-check-input" type="checkbox" id="selectAllDevices" checked style="background-color: #3c3c3c; border-color: #555555;">
                    </div>
                  </th>
                  <th>Device Name</th>
                  <th>IP Address</th>
                  <th>Status</th>
                </tr>
              </thead>
              <tbody id="selectedDevicesTableBody">
                <!-- Selected devices will be added here dynamically -->
              </tbody>
            </table>
          </div>
        </div>
      </div>


      <!-- Firmware Configuration Card -->
      <div class="card mt-4" style="background-color: var(--vs-code-lighter-bg); border: 1px solid var(--vs-code-border); border-radius: 6px;">
        <div class="card-header" style="background-color: #2d2d2d; border-bottom: 1px solid var(--vs-code-border);">
          <h5 class="mb-0" style="color: #cccccc;">
            <i class="bi bi-gear-fill me-2" style="color: #007acc;"></i>Firmware Configuration
          </h5>
        </div>
        <div class="card-body p-4">
          <!-- Device Type Selection -->
          <div class="mb-4">
            <label for="deviceTypeSelect" class="form-label fw-bold" style="color: #cccccc;">
              <i class="bi bi-cpu me-2" style="color: #007acc;"></i>Select Device Type
            </label>
            <select class="form-select form-select-lg" id="deviceTypeSelect" aria-label="Device type selection"
                  style="background-color: #3c3c3c; border-color: #555555; color: var(--vs-code-text);">
              <option selected disabled>Select the device type</option>
              <option value="1">SVDGS</option>
              <option value="2">Operator Panel</option>
            </select>
          </div>

          <!-- Firmware File Selection -->
          <div class="mb-4">
            <label class="form-label fw-bold" style="color: #cccccc;">
              <i class="bi bi-file-earmark-binary me-2" style="color: #007acc;"></i>Select Firmware File
            </label>
            <div class="input-group input-group-lg">
              <input type="text" class="form-control" id="filePathInput" readonly placeholder="No file selected"
                   style="background-color: #3c3c3c; border-color: #555555; color: var(--vs-code-text);" />
              <button class="btn px-4" type="button" id="browseButton"
                    style="background-color: #007acc; color: white; border: none;">
                <i class="bi bi-folder2-open me-2"></i>Browse
              </button>
            </div>
            <small id="filePathDisplay" class="form-text mt-2 ms-2" style="color: #999999;">
              <i class="bi bi-info-circle me-1"></i>No file chosen
            </small>
          </div>

          <!-- Flash Button (Hidden by default) -->
          <div class="input-box mt-4" id="firmwareBox" style="display: none;">
            <button class="btn btn-lg w-100 py-3" style="background-color: #3c873a; color: white; border: none;">
              <i class="bi bi-lightning-charge-fill me-2"></i>Flash Firmware
            </button>
          </div>
        </div>
      </div>

    </div>
 </div>

 <!-- Log Console - Fixed at bottom with full width -->
 <div class="container-fluid fixed-bottom p-0" style="z-index: 1000;">
  <div class="row m-0">
    <div class="col-12 p-0">
      <!-- Operation Log Console -->
      <div id="operationLogConsole"
           class="p-3 border-top log-console"
           style="height: 300px; overflow-y: auto; font-family: monospace; font-size: 0.8rem; display: none; background-color: #1e1e1e !important; border-top: 1px solid var(--vs-code-border) !important; color: var(--vs-code-text);">
        <div class="d-flex justify-content-between align-items-center mb-2">
          <h6 class="mb-0" style="color: #cccccc;">Terminal</h6>
          <button class="btn btn-sm" id="toggleOperationLog" style="background-color: #252526; color: #cccccc; border: 1px solid #3e3e42;">
            <i class="bi bi-chevron-down"></i>
          </button>
        </div>
        <div id="operationLogContent">
          <!-- Log entries will be added here dynamically -->
        </div>
      </div>
    </div>
  </div>
</div>


    <!-- Include renderer.js script -->
    <script src="renderer.js"></script>
  </body>
</html>
