<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
  <title>Smart Docking AI Firmware Flasher</title>
  <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.5/dist/css/bootstrap.min.css" rel="stylesheet"
    integrity="sha384-SgOJa3DmI69IUzQ2PVdRZhwQ+dy64/BUtbMJw1MZ8t5HZApcHrRKUc4W0kG879m7" crossorigin="anonymous">
  <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.1/font/bootstrap-icons.css">
  <link rel="stylesheet" href="style.css">
  <style>
    :root {
      --vs-code-bg: #1e1e1e;
      --vs-code-lighter-bg: #252526;
      --vs-code-darker-bg: #1a1a1a;
      --vs-code-text: #d4d4d4;
      --vs-code-border: #3e3e42;
    }

    body {
      /* Add padding to prevent content from being hidden behind fixed elements */
      padding-bottom: 350px;
      padding-top: 0;
      margin-top: 0;
      background-color: var(--vs-code-bg) !important;
      color: var(--vs-code-text);
    }

    /* Style for the fixed header */
    .heading-box {
      background-color: var(--vs-code-darker-bg);
      box-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
      border-bottom: 1px solid var(--vs-code-border);
    }

    .heading-box h3 {
      font-weight: 600;
      color: #cccccc;
    }

    .discovery-card {
      border-radius: 6px;
      box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
      background-color: var(--vs-code-lighter-bg);
      border: 1px solid var(--vs-code-border);
      color: var(--vs-code-text);
    }

    .option-card {
      cursor: pointer;
      transition: all 0.3s;
      border: 2px solid transparent;
      background-color: var(--vs-code-lighter-bg);
      color: var(--vs-code-text);
    }

    .option-card.active {
      border-color: #007acc;
      background-color: rgba(0, 122, 204, 0.2);
    }

    .scan-btn {
      background-color: #007acc;
      color: white;
      border-radius: 3px;
      padding: 10px 20px;
      font-weight: 500;
      border: none;
    }

    .scan-btn:hover {
      background-color: #0062a3;
      color: white;
    }

    .continue-btn {
      background-color: #3c873a;
      color: white;
      border-radius: 3px;
      padding: 10px 30px;
      font-weight: 500;
      transition: all 0.3s ease;
      border: none;
    }

    .continue-btn:hover:not(:disabled) {
      background-color: #2d6a2c;
      color: white;
      box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
      cursor: pointer;
    }

    .continue-btn:disabled {
      background-color: #4d4d4d !important;
      color: #999999 !important;
      opacity: 0.65;
      cursor: not-allowed;
    }

    /* Styles for the log console */
    .log-console {
      transition: height 0.3s ease;
      background-color: #1e1e1e !important;
      border-top: 1px solid var(--vs-code-border) !important;
    }

    .log-console.collapsed {
      height: 40px !important;
      overflow: hidden;
    }

    .device-table {
      border-radius: 4px;
      overflow: hidden;
      color: var(--vs-code-text);
    }

    .device-table thead {
      background-color: #2d2d2d;
      color: #cccccc;
    }

    .table {
      color: var(--vs-code-text);
      border-color: var(--vs-code-border);
    }

    .table-hover tbody tr:hover {
      background-color: #2a2d2e;
    }

    .table-light {
      background-color: #2d2d2d;
      color: #cccccc;
    }

    .progress {
      height: 8px;
      background-color: #333333;
    }

    .progress-bar {
      background-color: #007acc;
    }

    .form-check-input:checked {
      background-color: #007acc;
      border-color: #007acc;
    }

    .form-check-input {
      background-color: #3c3c3c;
      border-color: #555555;
    }

    .form-control {
      background-color: #3c3c3c;
      border-color: #555555;
      color: var(--vs-code-text);
    }

    .form-control:focus {
      background-color: #3c3c3c;
      border-color: #007acc;
      color: var(--vs-code-text);
      box-shadow: 0 0 0 0.25rem rgba(0, 122, 204, 0.25);
    }

    .form-text {
      color: #999999;
    }

    .nav-tabs {
      border-bottom-color: var(--vs-code-border);
    }

    .nav-tabs .nav-link {
      color: #cccccc;
      border: none;
    }

    .nav-tabs .nav-link.active {
      color: #ffffff;
      background-color: #2d2d2d;
      border-color: var(--vs-code-border);
      border-bottom-color: transparent;
    }

    .nav-tabs .nav-link:hover:not(.active) {
      border-color: transparent;
      background-color: #2a2d2e;
    }

    .badge {
      font-weight: normal;
    }

    .text-muted {
      color: #999999 !important;
    }
  </style>
</head>
<body>
  <!-- Fixed Header at the top -->
  <div class="container-fluid fixed-top p-0 m-0" style="z-index: 1000; background-color: var(--vs-code-darker-bg); top: 0;">
    <div class="row m-0">
      <div class="col-12 py-2 text-center heading-box border-bottom">
        <h3 class="m-0 fs-6">Smart Docking AI Firmware Flasher</h3>
      </div>
    </div>
  </div>

  <!-- Main content with centered card -->
  <div class="container py-4" style="margin-left: 400px; width: calc(100% - 400px);">
    <!-- Spacer to prevent content from being hidden behind the fixed header -->
    <div style="height: 60px;"></div>

    <!-- Centered card with max-width -->
    <div class="row justify-content-center">
      <div class="col-12 col-lg-10 col-xl-10">
        <div class="card discovery-card mb-4">
      <div class="card-body p-4 ">
        <!-- Device Discovery Tabs with Refresh Button -->
        <div class="d-flex justify-content-between align-items-center mb-4">
          <ul class="nav nav-tabs mb-0" id="discoveryTabs" role="tablist">
            <li class="nav-item" role="presentation">
              <button class="nav-link active" id="scan-tab" data-bs-toggle="tab" data-bs-target="#scan-content" type="button" role="tab" aria-controls="scan-content" aria-selected="true">
                <i class="bi bi-search me-2"></i>Scan Devices
              </button>
            </li>
            <li class="nav-item" role="presentation">
              <button class="nav-link" id="manual-tab" data-bs-toggle="tab" data-bs-target="#manual-content" type="button" role="tab" aria-controls="manual-content" aria-selected="false">
                <i class="bi bi-pencil-square me-2"></i>Add Manually
              </button>
            </li>
          </ul>

          <!-- Refresh Button -->
          <button id="refreshButton" class="btn" style="background-color: #252526; color: #cccccc; border: 1px solid #3e3e42;">
            <i class="bi bi-arrow-clockwise me-2"></i>Refresh Page
          </button>
        </div>

        <!-- Tab Content -->
        <div class="tab-content" id="discoveryTabContent">
          <!-- Scan Devices Tab -->
          <div class="tab-pane fade show active" id="scan-content" role="tabpanel" aria-labelledby="scan-tab">
            <!-- IP Range Selection -->
            <div class="card mb-4" style="background-color: #252526; border: 1px solid #3e3e42;">
              <div class="card-header" style="background-color: #2d2d2d; border-bottom: 1px solid #3e3e42;">
                <h6 class="mb-0" style="color: #cccccc;">IP Range Configuration</h6>
              </div>
              <div class="card-body">
                <div class="row g-3">
                  <div class="col-md-6">
                    <label for="subnetSelect" class="form-label" style="color: #cccccc;">Subnet</label>
                    <select id="subnetSelect" class="form-select" style="background-color: #3c3c3c; color: #cccccc; border-color: #555555;">
                      <option value="192.168.29">192.168.29.x</option>
                      <option value="192.168.2">192.168.2.x</option>
                      <option value="custom">Custom...</option>
                    </select>
                  </div>
                  <div class="col-md-6" id="customSubnetContainer" style="display: none;">
                    <label for="customSubnet" class="form-label" style="color: #cccccc;">Custom Subnet</label>
                    <input type="text" id="customSubnet" class="form-control" placeholder="192.168.x"
                           style="background-color: #3c3c3c; color: #cccccc; border-color: #555555;">
                  </div>
                  <div class="col-md-6">
                    <label for="startIp" class="form-label" style="color: #cccccc;">Start IP (Last Octet)</label>
                    <input type="number" id="startIp" class="form-control" value="2" min="1" max="254"
                           style="background-color: #3c3c3c; color: #cccccc; border-color: #555555;">
                  </div>
                  <div class="col-md-6">
                    <label for="endIp" class="form-label" style="color: #cccccc;">End IP (Last Octet)</label>
                    <input type="number" id="endIp" class="form-control" value="254" min="1" max="254"
                           style="background-color: #3c3c3c; color: #cccccc; border-color: #555555;">
                  </div>
                </div>
              </div>
            </div>

            <div class="d-grid gap-2 mb-4">
              <button id="scanButton" class="btn scan-btn py-3">
                <i class="bi bi-search me-2"></i>Scan for SVDGS Devices
              </button>
            </div>

            <!-- Scanning Progress -->
            <div id="scanProgress" class="mb-4" style="display: none;">
              <div class="d-flex justify-content-between mb-1">
                <span>Scanning network for devices...</span>
                <span id="scanPercentage">0%</span>
              </div>
              <div class="progress mb-3">
                <div class="progress-bar progress-bar-striped progress-bar-animated" role="progressbar" style="width: 0%"></div>
              </div>
            </div>

            <!-- Scan Results -->
            <div id="scanResults" class="mt-4" style="display: none;">
              <div class="d-flex justify-content-between align-items-center mb-3">
                <h6 class="mb-0 fw-bold">Available Devices:</h6>
                <span class="badge bg-primary rounded-pill" id="deviceCount">0</span>
              </div>
              <div class="table-responsive">
                <table class="table table-hover device-table">
                  <thead class="table-light">
                    <tr>
                      <th width="50">
                        <div class="form-check">
                          <input class="form-check-input" type="checkbox" id="selectAllDevices">
                        </div>
                      </th>
                      <th>Device Name</th>
                      <th>IP Address</th>
                      <th>Status</th>
                    </tr>
                  </thead>
                  <tbody id="deviceTableBody">
                    <!-- Devices will be added here dynamically -->
                  </tbody>
                </table>
              </div>
            </div>
          </div>

          <!-- Manual Entry Tab -->
          <div class="tab-pane fade" id="manual-content" role="tabpanel" aria-labelledby="manual-tab">
            <div class="mb-4">
              <label for="manualIpInput" class="form-label fw-bold">Enter IP Address(es)</label>
              <div class="input-group mb-2">
                <input type="text" class="form-control" id="manualIpInput" placeholder="************, ************, ************">
                <button id="addManualIpButton" class="btn scan-btn">
                  <i class="bi bi-plus-circle me-2"></i>Add
                </button>
              </div>
              <div class="form-text">Enter single or multiple IP addresses separated by commas</div>

              <!-- Manual Check Progress -->
              <div id="manualCheckProgress" class="mt-3" style="display: none;">
                <div class="d-flex justify-content-between mb-1">
                  <span>Checking IP addresses...</span>
                  <span id="manualCheckStatus">In Progress</span>
                </div>
              </div>
            </div>

            <!-- Manual Entry Results -->
            <div id="manualResults" class="mt-4" style="display: none;">
              <div class="d-flex justify-content-between align-items-center mb-3">
                <h6 class="mb-0 fw-bold">Added Devices:</h6>
                <span class="badge bg-primary rounded-pill" id="manualDeviceCount">0</span>
              </div>
              <div class="table-responsive">
                <table class="table table-hover device-table">
                  <thead class="table-light">
                    <tr>
                      <th width="50">
                        <div class="form-check">
                          <input class="form-check-input" type="checkbox" id="selectAllManualDevices">
                        </div>
                      </th>
                      <th>Device Name</th>
                      <th>IP Address</th>
                      <th>Status</th>
                    </tr>
                  </thead>
                  <tbody id="manualDeviceTableBody">
                    <!-- Manually added devices will appear here -->
                  </tbody>
                </table>
              </div>
            </div>
          </div>
        </div>

        <!-- Continue Button -->
        <div id="continueButtonContainer" class="mt-4 pt-3 border-top" style="display: none;">
          <div class="d-flex justify-content-between align-items-center">
            <div>
              <span class="fw-bold" id="selectedDevicesCount">0 devices selected</span>
              <p class="text-muted mb-0 small">Select devices to continue with firmware update</p>
            </div>
            <button id="continueButton" class="btn continue-btn px-4 py-2" onclick="window.location.href='index.html'">
              <i class="bi bi-arrow-right-circle me-2"></i>Continue to Firmware Update
            </button>
          </div>
        </div>
      </div>
    </div>
      </div>
    </div>
  </div>



<!-- Unified Terminal - Fixed at bottom with full width -->
<div class="container-fluid fixed-bottom p-0" style="z-index: 1000;">
  <div class="row m-0">
    <div class="col-12 p-0">
      <!-- Unified Terminal Console -->
      <!-- Terminal with fixed header -->
      <div id="unifiedTerminal" class="border-top" style="height: 300px; display: block; background-color: #1e1e1e !important; border-top: 1px solid var(--vs-code-border) !important; color: var(--vs-code-text);">
        <!-- Fixed header -->
        <div class="d-flex justify-content-between align-items-center p-1 border-bottom" style="background-color: #1e1e1e; border-bottom: 1px solid var(--vs-code-border) !important; position: sticky; top: 0; z-index: 100;">
          <h6 class="mb-0 " style="color: #cccccc; font-weight: bold;">Terminal</h6>
          <div>
            <button class="btn btn-sm" id="toggleTerminal" style="background-color: #252526; color: #cccccc; border: 1px solid #3e3e42;">
              <i class="bi bi-chevron-down"></i>
            </button>
          </div>
        </div>
        <!-- Scrollable content area -->
        <div id="terminalContent" class="p-3" style="height: calc(100% - 40px); overflow-y: auto; font-family: monospace; font-size: 0.8rem;"></div>
      </div>
    </div>
  </div>
</div>


  <!-- Inline script for device selection and navigation -->
  <script>
    // Function to store selected devices in localStorage before navigating
    function storeSelectedDevices() {
      console.log('Storing selected devices...');

      try {
        // Get active tab
        const activeTab = document.querySelector('.nav-link.active').id === 'scan-tab' ? 'scan' : 'manual';
        const tableBody = activeTab === 'scan' ?
                        document.getElementById('deviceTableBody') :
                        document.getElementById('manualDeviceTableBody');

        if (!tableBody) {
          console.error('tableBody is null or undefined');
          return false;
        }

        // Get selected checkboxes
        const selectedCheckboxes = tableBody.querySelectorAll('.device-checkbox:checked');
        console.log(`Found ${selectedCheckboxes.length} selected checkboxes`);

        if (selectedCheckboxes.length === 0) {
          console.warn('No specific devices selected, using all valid discovered devices');

          // Instead of preventing navigation, let's use all valid discovered devices
          // This will be handled by the allDiscoveredDevices array in device-discovery.js

          // We'll set a flag to indicate we're using all discovered devices
          localStorage.setItem('usingAllDiscoveredDevices', 'true');
          sessionStorage.setItem('usingAllDiscoveredDevices', 'true');

          return true;
        }

        // Extract device information from the table rows
        const selectedDevices = Array.from(selectedCheckboxes).map(checkbox => {
          const row = checkbox.closest('tr');
          return {
            id: parseInt(checkbox.value),
            name: row.cells[1].textContent,
            ip: row.cells[2].textContent,
            status: row.cells[3].querySelector('.badge').textContent.trim(),
            isValid: row.cells[3].querySelector('.badge').textContent.trim() === 'Online' ? true : false
          };
        });

        console.log('Selected devices:', selectedDevices);

        // Store in localStorage - use sessionStorage for more reliability
        localStorage.setItem('selectedDevices', JSON.stringify(selectedDevices));
        sessionStorage.setItem('selectedDevices', JSON.stringify(selectedDevices));

        // Also store as a backup with a timestamp
        const timestamp = new Date().getTime();
        localStorage.setItem(`selectedDevices_${timestamp}`, JSON.stringify(selectedDevices));

        // Show loading state
        const continueButton = document.getElementById('continueButton');
        if (continueButton) {
          continueButton.innerHTML = '<span class="spinner-border spinner-border-sm me-2"></span>Loading...';
        }

        // Log the stored data for debugging
        console.log('Data stored in localStorage:', localStorage.getItem('selectedDevices'));
        console.log('Data stored in sessionStorage:', sessionStorage.getItem('selectedDevices'));

        return true; // Allow the navigation to proceed
      } catch (error) {
        console.error('Error in storeSelectedDevices:', error);
        // Continue anyway to ensure navigation works
        return true;
      }
    }
  </script>



<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.5/dist/js/bootstrap.bundle.min.js"
integrity="sha384-k6d4wzSIapyDyv1kpU366/PK5hCdSbCRGRCMv+eplOQJWyd1fbcAu9OCUj5zNLiq" crossorigin="anonymous"></script>

  <script src="device-discovery.js"></script>
    <!-- Include renderer.js script -->
    <script src="renderer.js"></script>

</body>
</html>
