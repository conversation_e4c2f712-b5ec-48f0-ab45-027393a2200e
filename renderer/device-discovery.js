// DOM Elements
const scanTab = document.getElementById('scan-tab');
const manualTab = document.getElementById('manual-tab');
const scanContent = document.getElementById('scan-content');
const manualContent = document.getElementById('manual-content');
const scanButton = document.getElementById('scanButton');
const scanProgress = document.getElementById('scanProgress');
const scanPercentage = document.getElementById('scanPercentage');
const progressBar = scanProgress ? scanProgress.querySelector('.progress-bar') : null;
const scanResults = document.getElementById('scanResults');
const deviceTableBody = document.getElementById('deviceTableBody');
const deviceCount = document.getElementById('deviceCount');
const manualIpInput = document.getElementById('manualIpInput');
const addManualIpButton = document.getElementById('addManualIpButton');
const manualResults = document.getElementById('manualResults');
const manualDeviceTableBody = document.getElementById('manualDeviceTableBody');
const manualDeviceCount = document.getElementById('manualDeviceCount');
const selectAllDevices = document.getElementById('selectAllDevices');
const selectAllManualDevices = document.getElementById('selectAllManualDevices');
const continueButtonContainer = document.getElementById('continueButtonContainer');
const continueButton = document.getElementById('continueButton');
const selectedDevicesCount = document.getElementById('selectedDevicesCount');

// IP Range Configuration Elements
const subnetSelect = document.getElementById('subnetSelect');
const customSubnetContainer = document.getElementById('customSubnetContainer');
const customSubnet = document.getElementById('customSubnet');
const startIp = document.getElementById('startIp');
const endIp = document.getElementById('endIp');

// Unified terminal elements
const unifiedTerminal = document.getElementById('unifiedTerminal');
const terminalContent = document.getElementById('terminalContent');
const toggleTerminal = document.getElementById('toggleTerminal');
const manualCheckProgress = document.getElementById('manualCheckProgress');
const manualCheckStatus = document.getElementById('manualCheckStatus');

// Define these variables to maintain compatibility with existing code
const scanLogConsole = unifiedTerminal;
const scanLogContent = terminalContent;
const manualLogConsole = unifiedTerminal;
const manualLogContent = terminalContent;

// Debug function to check if elements exist
function checkElements() {
    console.log('Checking device discovery UI elements:');
    console.log('scanTab:', scanTab ? 'Found' : 'Not found');
    console.log('manualTab:', manualTab ? 'Found' : 'Not found');
    console.log('scanButton:', scanButton ? 'Found' : 'Not found');
    console.log('scanProgress:', scanProgress ? 'Found' : 'Not found');
    console.log('progressBar:', progressBar ? 'Found' : 'Not found');
    console.log('scanResults:', scanResults ? 'Found' : 'Not found');
    console.log('deviceTableBody:', deviceTableBody ? 'Found' : 'Not found');
    console.log('manualIpInput:', manualIpInput ? 'Found' : 'Not found');
    console.log('addManualIpButton:', addManualIpButton ? 'Found' : 'Not found');
    console.log('manualResults:', manualResults ? 'Found' : 'Not found');
    console.log('manualDeviceTableBody:', manualDeviceTableBody ? 'Found' : 'Not found');
    console.log('continueButton:', continueButton ? 'Found' : 'Not found');
    console.log('scanLogConsole:', scanLogConsole ? 'Found' : 'Not found');
    console.log('scanLogContent:', scanLogContent ? 'Found' : 'Not found');
    console.log('manualCheckProgress:', manualCheckProgress ? 'Found' : 'Not found');
    console.log('manualLogConsole:', manualLogConsole ? 'Found' : 'Not found');
    console.log('manualLogContent:', manualLogContent ? 'Found' : 'Not found');
    console.log('manualCheckStatus:', manualCheckStatus ? 'Found' : 'Not found');
}

// Store discovered devices
let discoveredDevices = [];
let manualDevices = [];
let activeTab = 'scan'; // Default active tab

// Global array to store all discovered devices from both scan and manual methods
let allDiscoveredDevices = [];

// Load any previously discovered devices from storage
function loadAllDiscoveredDevices() {
    try {
        // Try to get from sessionStorage first (more reliable between page navigations)
        let devicesJson = sessionStorage.getItem('allDiscoveredDevices');

        // If not in sessionStorage, try localStorage
        if (!devicesJson) {
            devicesJson = localStorage.getItem('allDiscoveredDevices');
        }

        if (devicesJson) {
            allDiscoveredDevices = JSON.parse(devicesJson);
            console.log(`Loaded ${allDiscoveredDevices.length} previously discovered devices from storage`);
        }
    } catch (error) {
        console.error('Error loading discovered devices:', error);
        allDiscoveredDevices = [];
    }
}

// Store all discovered devices to both localStorage and sessionStorage
function storeAllDiscoveredDevices() {
    try {
        // Filter to only include valid devices
        const validDevices = allDiscoveredDevices.filter(device => device.isValid);

        // Store all devices (including invalid ones)
        const allDevicesJson = JSON.stringify(allDiscoveredDevices);
        localStorage.setItem('allDiscoveredDevices', allDevicesJson);
        sessionStorage.setItem('allDiscoveredDevices', allDevicesJson);

        // Also store only valid devices for easier access
        const validDevicesJson = JSON.stringify(validDevices);
        localStorage.setItem('validDiscoveredDevices', validDevicesJson);
        sessionStorage.setItem('validDiscoveredDevices', validDevicesJson);

        // IMPORTANT: Also store valid devices as selectedDevices to ensure they appear on the second page
        localStorage.setItem('selectedDevices', validDevicesJson);
        sessionStorage.setItem('selectedDevices', validDevicesJson);

        // Store with timestamp for backup
        const timestamp = new Date().getTime();
        localStorage.setItem(`selectedDevices_${timestamp}`, validDevicesJson);

        // Log the stored data for debugging
        console.log(`Stored ${allDiscoveredDevices.length} total devices (${validDevices.length} valid) in storage`);
        console.log('Valid devices also stored as selectedDevices for second page');
        console.log('Data stored in localStorage:', localStorage.getItem('selectedDevices'));
    } catch (error) {
        console.error('Error storing all discovered devices:', error);
    }
}

// Variable to track if user has manually scrolled
let userHasScrolled = false;

// Function to add log message to the unified terminal
function addTerminalLog(message, type = 'info') {
    if (!unifiedTerminal || !terminalContent) {
        console.error('unifiedTerminal or terminalContent element not found');
        return;
    }

    // Create a new log entry
    const logEntry = document.createElement('div');

    // Format the timestamp
    const timestamp = new Date().toLocaleTimeString();

    // Set the appropriate class based on message type
    let className = '';
    switch(type) {
        case 'error':
            className = 'text-danger';
            break;
        case 'success':
            className = 'text-success';
            break;
        case 'warning':
            className = 'text-warning';
            break;
        default:
            className = 'text-info';
    }

    // Set the HTML content
    logEntry.innerHTML = `<span class="text-secondary">[${timestamp}]</span> <span class="${className}">${message}</span>`;

    // Add to the log content
    terminalContent.appendChild(logEntry);

    // Auto-scroll to the bottom only if user hasn't manually scrolled up
    if (!userHasScrolled) {
        terminalContent.scrollTop = terminalContent.scrollHeight;
    }
}

// Function to add log message to scan console
function addScanLog(message, type = 'info') {
    // Add to the unified terminal
    addTerminalLog(message, type);
}

// Function to add log message to manual console
function addManualLog(message, type = 'info') {
    // Show the progress container if it's hidden
    if (manualCheckProgress) manualCheckProgress.style.display = 'block';

    // Add to the unified terminal
    addTerminalLog(message, type);
}

// Initialize the UI
function initUI() {
    console.log('Initializing device discovery UI...');
    checkElements();

    // Load any previously discovered devices
    loadAllDiscoveredDevices();

    // Set up refresh button
    const refreshButton = document.getElementById('refreshButton');
    if (refreshButton) {
        refreshButton.addEventListener('click', function() {
            console.log('Refresh button clicked');
            addTerminalLog('Refreshing page...', 'info');

            // Show loading state
            refreshButton.disabled = true;
            refreshButton.innerHTML = '<span class="spinner-border spinner-border-sm me-2"></span>Refreshing...';

            // Reload the page after a short delay
            setTimeout(() => {
                window.location.reload();
            }, 500);
        });
    }

    // Set up subnet select change handler
    if (subnetSelect) {
        subnetSelect.addEventListener('change', function() {
            if (this.value === 'custom') {
                // Show custom subnet input
                if (customSubnetContainer) {
                    customSubnetContainer.style.display = 'block';
                }
            } else {
                // Hide custom subnet input
                if (customSubnetContainer) {
                    customSubnetContainer.style.display = 'none';
                }
            }
        });
    }

    // Initialize the unified terminal and make it visible by default
    if (unifiedTerminal && terminalContent) {
        addTerminalLog('Welcome to Smart Docking AI Firmware Flasher', 'info');
        addTerminalLog('System initialized and ready', 'success');
        addTerminalLog('Please scan for devices or add them manually', 'info');

        // Set up toggle button for terminal
        if (toggleTerminal) {
            toggleTerminal.addEventListener('click', () => {
                const currentHeight = unifiedTerminal.style.height;
                if (currentHeight === '40px') {
                    // Expand
                    unifiedTerminal.style.height = '300px';
                    toggleTerminal.innerHTML = '<i class="bi bi-chevron-down"></i>';
                    terminalContent.style.display = 'block';
                } else {
                    // Collapse but keep header visible
                    unifiedTerminal.style.height = '40px';
                    toggleTerminal.innerHTML = '<i class="bi bi-chevron-up"></i>';
                    terminalContent.style.display = 'none';
                }
            });
        }

        // Add scroll event listener to detect manual scrolling
        terminalContent.addEventListener('scroll', function() {
            // Check if user has scrolled up (not at the bottom)
            const isScrolledToBottom = terminalContent.scrollHeight - terminalContent.clientHeight <= terminalContent.scrollTop + 50;
            userHasScrolled = !isScrolledToBottom;

            // Add a "scroll to bottom" button if user has scrolled up
            if (userHasScrolled) {
                // Check if button already exists
                let scrollButton = document.getElementById('scrollToBottomBtn');
                if (!scrollButton) {
                    scrollButton = document.createElement('button');
                    scrollButton.id = 'scrollToBottomBtn';
                    scrollButton.className = 'btn btn-sm position-absolute';
                    scrollButton.style.bottom = '10px';
                    scrollButton.style.right = '10px';
                    scrollButton.style.zIndex = '200';
                    scrollButton.style.backgroundColor = '#252526';
                    scrollButton.style.color = '#cccccc';
                    scrollButton.style.border = '1px solid #3e3e42';
                    scrollButton.innerHTML = '<i class="bi bi-arrow-down-circle"></i>';
                    scrollButton.title = 'Scroll to bottom';
                    scrollButton.onclick = function() {
                        terminalContent.scrollTop = terminalContent.scrollHeight;
                        userHasScrolled = false;
                        this.remove();
                    };
                    unifiedTerminal.appendChild(scrollButton);
                }
            } else {
                // Remove button if user is at the bottom
                const scrollButton = document.getElementById('scrollToBottomBtn');
                if (scrollButton) {
                    scrollButton.remove();
                }
            }
        });
    }

    // Track active tab for continue button logic
    if (scanTab) {
        scanTab.addEventListener('shown.bs.tab', () => {
            console.log('Scan tab activated');
            activeTab = 'scan';
            updateContinueButton();

            // Add a log message about tab switch
            addTerminalLog('Switched to Scan tab', 'info');
        });
    }

    if (manualTab) {
        manualTab.addEventListener('shown.bs.tab', () => {
            console.log('Manual tab activated');
            activeTab = 'manual';
            updateContinueButton();

            // Add a log message about tab switch
            addTerminalLog('Switched to Manual tab', 'info');
        });
    }

    // Set up scan button
    if (scanButton) {
        console.log('Adding click event listener to scanButton');
        scanButton.addEventListener('click', startScan);
    } else {
        console.error('scanButton element not found');
    }

    // Set up manual IP button
    if (addManualIpButton) {
        console.log('Adding click event listener to addManualIpButton');
        addManualIpButton.addEventListener('click', addManualDevices);
    } else {
        console.error('addManualIpButton element not found');
    }

    // Set up select all checkboxes with a more direct approach
    if (selectAllDevices) {
        console.log('Adding click event listener to selectAllDevices');

        // Use addEventListener instead of replacing the element
        selectAllDevices.addEventListener('change', function() {
            console.log('Select All Devices clicked, checked:', this.checked);

            if (!deviceTableBody) {
                console.error('deviceTableBody is null or undefined');
                return;
            }

            const checkboxes = deviceTableBody.querySelectorAll('.device-checkbox');
            console.log(`Found ${checkboxes.length} checkboxes in deviceTableBody`);

            // Update all checkboxes (no need to check for disabled)
            checkboxes.forEach(checkbox => {
                checkbox.checked = this.checked;
                console.log(`Set checkbox ${checkbox.value} to ${this.checked}`);

                // Trigger the change event to ensure any listeners are notified
                const event = new Event('change', { bubbles: true });
                checkbox.dispatchEvent(event);
            });

            // Force update the continue button
            console.log('Updating continue button after Select All');
            updateContinueButton();
        });
    }

    if (selectAllManualDevices) {
        console.log('Adding click event listener to selectAllManualDevices');

        // Use addEventListener instead of replacing the element
        selectAllManualDevices.addEventListener('change', function() {
            console.log('Select All Manual Devices clicked, checked:', this.checked);

            if (!manualDeviceTableBody) {
                console.error('manualDeviceTableBody is null or undefined');
                return;
            }

            const checkboxes = manualDeviceTableBody.querySelectorAll('.device-checkbox');
            console.log(`Found ${checkboxes.length} checkboxes in manualDeviceTableBody`);

            // Update all checkboxes (no need to check for disabled)
            checkboxes.forEach(checkbox => {
                checkbox.checked = this.checked;
                console.log(`Set manual checkbox ${checkbox.value} to ${this.checked}`);

                // Trigger the change event to ensure any listeners are notified
                const event = new Event('change', { bubbles: true });
                checkbox.dispatchEvent(event);
            });

            // Force update the continue button
            console.log('Updating continue button after Select All Manual');
            updateContinueButton();
        });
    }

    // Add direct event listeners to device tables for checkbox changes
    if (deviceTableBody) {
        deviceTableBody.addEventListener('change', (e) => {
            if (e.target.classList.contains('device-checkbox')) {
                console.log('Device checkbox changed directly');
                updateContinueButton();
            }
        });
    }

    if (manualDeviceTableBody) {
        manualDeviceTableBody.addEventListener('change', (e) => {
            if (e.target.classList.contains('device-checkbox')) {
                console.log('Manual device checkbox changed directly');
                updateContinueButton();
            }
        });
    }

    // Set up continue button with a more direct approach
    if (continueButton) {
        console.log('Adding click event listener to continueButton');

        // Clear any existing event listeners
        continueButton.replaceWith(continueButton.cloneNode(true));

        // Get the fresh reference
        continueButton = document.getElementById('continueButton');

        // Add event listener - use both onclick and addEventListener for redundancy
        continueButton.onclick = function() {
            console.log('Continue button clicked (onclick)');

            // Store selected devices before proceeding
            const result = storeSelectedDevices();

            // If specific devices were selected, they are already stored by storeSelectedDevices()
            if (result && localStorage.getItem('usingAllDiscoveredDevices') !== 'true') {
                console.log('Specific devices were selected, those will be used preferentially');

                // No need to overwrite the selected devices that were just stored
                console.log('Using specifically selected devices for the next page');

                // Navigate to the next page
                window.location.href = 'index.html';
                return true;
            }

            // If no specific devices were selected, use all valid discovered devices
            console.log('No specific devices selected, storing all valid discovered devices');

            // Filter only valid devices
            const validDevices = allDiscoveredDevices.filter(device => device.isValid);
            console.log(`Storing ${validDevices.length} valid discovered devices`);

            // Store in localStorage and sessionStorage
            localStorage.setItem('selectedDevices', JSON.stringify(validDevices));
            sessionStorage.setItem('selectedDevices', JSON.stringify(validDevices));

            // Also store as a backup with a timestamp
            const timestamp = new Date().getTime();
            localStorage.setItem(`selectedDevices_${timestamp}`, JSON.stringify(validDevices));

            // Navigate to the next page
            window.location.href = 'index.html';
            return true;
        };

        // Also add addEventListener as a backup
        continueButton.addEventListener('click', function() {
            console.log('Continue button clicked (addEventListener)');

            // Store selected devices before proceeding
            const result = storeSelectedDevices();

            // If specific devices were selected, they are already stored by storeSelectedDevices()
            if (result && localStorage.getItem('usingAllDiscoveredDevices') !== 'true') {
                console.log('Specific devices were selected, those will be used preferentially');

                // No need to overwrite the selected devices that were just stored
                console.log('Using specifically selected devices for the next page');

                // Navigate to the next page
                window.location.href = 'index.html';
                return;
            }

            // If no specific devices were selected, use all valid discovered devices
            console.log('No specific devices selected, storing all valid discovered devices');

            // Filter only valid devices
            const validDevices = allDiscoveredDevices.filter(device => device.isValid);
            console.log(`Storing ${validDevices.length} valid discovered devices`);

            // Store in localStorage and sessionStorage
            localStorage.setItem('selectedDevices', JSON.stringify(validDevices));
            sessionStorage.setItem('selectedDevices', JSON.stringify(validDevices));

            // Also store as a backup with a timestamp
            const timestamp = new Date().getTime();
            localStorage.setItem(`selectedDevices_${timestamp}`, JSON.stringify(validDevices));

            // Navigate to the next page
            window.location.href = 'index.html';
        });
    }

    // Set up toggle buttons for log consoles
    if (toggleScanLog) {
        console.log('Adding click event listener to toggleScanLog');
        toggleScanLog.addEventListener('click', () => {
            if (!scanLogConsole) return;

            if (scanLogConsole.classList.contains('collapsed')) {
                // Expand
                scanLogConsole.classList.remove('collapsed');
                toggleScanLog.innerHTML = '<i class="bi bi-chevron-down"></i>';
            } else {
                // Collapse
                scanLogConsole.classList.add('collapsed');
                toggleScanLog.innerHTML = '<i class="bi bi-chevron-up"></i>';
            }
        });
    }

    if (toggleManualLog) {
        console.log('Adding click event listener to toggleManualLog');
        toggleManualLog.addEventListener('click', () => {
            if (!manualLogConsole) return;

            if (manualLogConsole.classList.contains('collapsed')) {
                // Expand
                manualLogConsole.classList.remove('collapsed');
                toggleManualLog.innerHTML = '<i class="bi bi-chevron-down"></i>';
            } else {
                // Collapse
                manualLogConsole.classList.add('collapsed');
                toggleManualLog.innerHTML = '<i class="bi bi-chevron-up"></i>';
            }
        });
    }
}

// Scan for devices on the network
async function startScan() {
    console.log('Starting device scan...');

    // Check if window.electronAPI exists
    if (!window.electronAPI) {
        console.error('window.electronAPI is not defined');
        alert('Error: electronAPI is not available. Please restart the application.');
        return;
    }

    // Reset UI
    if (scanResults) scanResults.style.display = 'none';

    // Create scan progress element if it doesn't exist (might have been removed on previous scan)
    if (!scanProgress) {
        console.log('Recreating scan progress element');
        const scanContentDiv = document.getElementById('scan-content');
        if (scanContentDiv) {
            const progressDiv = document.createElement('div');
            progressDiv.id = 'scanProgress';
            progressDiv.className = 'progress mb-4';
            progressDiv.style.height = '30px';
            progressDiv.innerHTML = `
                <div class="progress-bar progress-bar-striped progress-bar-animated"
                     role="progressbar" style="width: 0%" aria-valuenow="0"
                     aria-valuemin="0" aria-valuemax="100">
                    <span id="scanPercentage" class="fw-bold">0%</span>
                </div>
            `;

            // Insert after the scan button container or at the beginning
            const scanButtonContainer = scanContentDiv.querySelector('.d-grid.gap-2.mb-4');
            if (scanButtonContainer) {
                scanButtonContainer.after(progressDiv);
            } else {
                scanContentDiv.prepend(progressDiv);
            }

            // Update references
            scanProgress = progressDiv;
            progressBar = scanProgress.querySelector('.progress-bar');
            scanPercentage = document.getElementById('scanPercentage');
        }
    }

    // Create scan button if it doesn't exist
    if (!scanButton) {
        console.log('Recreating scan button');
        const scanContentDiv = document.getElementById('scan-content');
        if (scanContentDiv) {
            const buttonContainer = document.createElement('div');
            buttonContainer.className = 'd-grid gap-2 mb-4';
            buttonContainer.innerHTML = `
                <button id="scanButton" class="btn btn-lg"
                        style="background-color: #007acc; color: white; border: none;">
                    <i class="bi bi-search me-2"></i>Scan for Devices
                </button>
            `;

            scanContentDiv.prepend(buttonContainer);

            // Update reference and add event listener
            scanButton = document.getElementById('scanButton');
            if (scanButton) {
                scanButton.addEventListener('click', startScan);
            }
        }
    }

    if (scanProgress) scanProgress.style.display = 'block';
    if (scanButton) {
        scanButton.disabled = true;
        scanButton.innerHTML = '<span class="spinner-border spinner-border-sm me-2"></span>Scanning...';
    }
    if (progressBar) progressBar.style.width = '0%';
    if (scanPercentage) scanPercentage.textContent = '0%';
    if (deviceTableBody) deviceTableBody.innerHTML = '';

    // Clear the terminal content
    if (terminalContent) {
        // Don't clear the terminal, just add a separator
        addTerminalLog('--- Starting new device scan ---', 'info');
    }

    discoveredDevices = [];

    // Get the subnet, start, and end values from the form
    let subnet = "192.168.29";
    if (subnetSelect) {
        if (subnetSelect.value === 'custom' && customSubnet && customSubnet.value) {
            subnet = customSubnet.value.trim();
        } else {
            subnet = subnetSelect.value;
        }
    }

    let start = 2;
    if (startIp && startIp.value) {
        start = parseInt(startIp.value);
        if (isNaN(start) || start < 1 || start > 254) {
            start = 2;
        }
    }

    let end = 254;
    if (endIp && endIp.value) {
        end = parseInt(endIp.value);
        if (isNaN(end) || end < 1 || end > 254 || end < start) {
            end = 254;
        }
    }

    // Add initial log message
    addScanLog('Starting device scan...', 'info');
    addScanLog(`Scanning IP range: ${subnet}.${start} to ${subnet}.${end}`, 'info');

    // Listen for scan progress events
    document.addEventListener('scan-progress', updateScanProgress);

    // Custom event listener for IP scan status
    document.addEventListener('ip-scan-status', updateIpScanStatus);

    function updateScanProgress(event) {
        const { current, total, percentage } = event.detail;
        if (progressBar) progressBar.style.width = `${percentage}%`;
        if (scanPercentage) scanPercentage.textContent = `${percentage}%`;
        console.log(`Scan progress: ${current}/${total} (${percentage}%)`);
    }

    function updateIpScanStatus(event) {
        const { ip, status, message } = event.detail;
        let type = 'info';

        if (status === 'success') {
            type = 'success';
        } else if (status === 'error') {
            type = 'error';
        } else if (status === 'warning') {
            type = 'warning';
        }

        addScanLog(`IP ${ip}: ${message}`, type);
    }

    try {
        console.log('Calling window.electronAPI.scanDevices()');
        console.log(`Scanning subnet: ${subnet}, range: ${start}-${end}`);

        // Start the scan with the custom IP range
        const scanResult = await window.electronAPI.scanDevices({
            subnet: subnet,
            start: start,
            end: end
        });
        console.log('Scan result:', scanResult);

        // Process results
        discoveredDevices = scanResult.map((device, index) => {
            const newDevice = {
                id: index + 1,
                name: device.hostname || 'Unknown',
                ip: device.ip,
                status: device.isValid ? 'Online' : 'Offline',
                isValid: device.isValid
            };

            // Add to global array if it doesn't already exist
            const existingDeviceIndex = allDiscoveredDevices.findIndex(d => d.ip === device.ip);
            if (existingDeviceIndex === -1 && device.isValid) {
                allDiscoveredDevices.push(newDevice);
                console.log(`Added device ${device.ip} to global array from scan`);
            } else if (existingDeviceIndex !== -1 && device.isValid) {
                // Update existing device if status changed
                allDiscoveredDevices[existingDeviceIndex] = newDevice;
                console.log(`Updated device ${device.ip} in global array from scan`);
            }

            return newDevice;
        });

        // Store all discovered devices
        storeAllDiscoveredDevices();

        // Display the results
        if (discoveredDevices.length > 0) {
            // Update device count
            if (deviceCount) deviceCount.textContent = discoveredDevices.length;

            // Add success message to log
            addScanLog(`Scan completed. Found ${discoveredDevices.length} devices.`, 'success');

            // Show results and render table
            if (scanResults) scanResults.style.display = 'block';
            renderDeviceTable(discoveredDevices, deviceTableBody);
            updateContinueButton();
        } else {
            // Add warning message to log
            addScanLog('Scan completed. No devices found.', 'warning');

            if (deviceTableBody) {
                deviceTableBody.innerHTML = `
                    <tr>
                        <td colspan="4" class="text-center py-4">
                            <i class="bi bi-exclamation-circle text-warning fs-3 d-block mb-2"></i>
                            No devices found on the network
                        </td>
                    </tr>
                `;
            }
            if (deviceCount) deviceCount.textContent = '0';
            if (scanResults) scanResults.style.display = 'block';
        }
    } catch (error) {
        console.error('Error scanning devices:', error);

        // Add error message to log
        addScanLog(`Error scanning devices: ${error.message}`, 'error');

        if (deviceTableBody) {
            deviceTableBody.innerHTML = `
                <tr>
                    <td colspan="4" class="text-center py-4">
                        <i class="bi bi-exclamation-triangle text-danger fs-3 d-block mb-2"></i>
                        Error scanning devices: ${error.message}
                    </td>
                </tr>
            `;
        }
        if (scanResults) scanResults.style.display = 'block';
    } finally {
        // Clean up event listeners
        document.removeEventListener('scan-progress', updateScanProgress);
        document.removeEventListener('ip-scan-status', updateIpScanStatus);

        // Don't remove the scan button container anymore - we want to allow rescanning
        // Instead, just re-enable the button
        if (scanButton) {
            scanButton.disabled = false;
            scanButton.innerHTML = '<i class="bi bi-search me-2"></i>Scan Again';
            console.log('Scan button re-enabled for rescanning');
        }

        // Hide the progress bar but don't remove it
        if (scanProgress) {
            scanProgress.style.display = 'none';
            console.log('Scan progress hidden');
        }
    }
}

// Add manually entered devices
async function addManualDevices() {
    console.log('Adding manual devices...');

    // Check if window.electronAPI exists
    if (!window.electronAPI) {
        console.error('window.electronAPI is not defined');
        alert('Error: electronAPI is not available. Please restart the application.');
        return;
    }

    if (!manualIpInput) {
        console.error('manualIpInput element not found');
        return;
    }

    const ipInput = manualIpInput.value.trim();

    if (!ipInput) {
        alert('Please enter at least one IP address');
        return;
    }

    // Split by comma and trim each entry
    const ips = ipInput.split(',').map(ip => ip.trim());

    // Validate IP format
    const validIps = ips.filter(ip => {
        return /^(192\.168\.(29|2)\.\d{1,3})$/.test(ip);
    });

    if (validIps.length === 0) {
        alert('Please enter valid IP addresses in the format 192.168.29.x or 192.168.2.x');
        return;
    }

    // Add a separator to the terminal
    addTerminalLog('--- Starting manual IP check ---', 'info');

    // Show the progress container
    if (manualCheckProgress) manualCheckProgress.style.display = 'block';

    // Update status
    if (manualCheckStatus) manualCheckStatus.textContent = 'In Progress';
    if (manualCheckStatus) manualCheckStatus.className = 'badge bg-info text-dark';

    // Add initial log message
    addManualLog('Checking IP addresses...', 'info');
    addManualLog(`IPs to check: ${validIps.join(', ')}`, 'info');

    // Disable button and show loading state
    if (addManualIpButton) {
        addManualIpButton.disabled = true;
        addManualIpButton.innerHTML = '<span class="spinner-border spinner-border-sm me-2"></span>Checking...';
    }

    try {
        console.log('Checking manual IPs:', validIps);

        // Check each IP one by one to show progress in the log
        const deviceResults = [];

        for (const ip of validIps) {
            addManualLog(`Checking IP: ${ip}...`, 'info');

            try {
                const result = await window.electronAPI.checkDevice(ip);

                if (result.isValid) {
                    addManualLog(`IP ${ip}: Connected successfully (${result.hostname})`, 'success');
                } else {
                    addManualLog(`IP ${ip}: Reachable but not a valid device`, 'warning');
                }

                deviceResults.push(result);
            } catch (error) {
                addManualLog(`IP ${ip}: Error - ${error.message}`, 'error');
                deviceResults.push({
                    ip,
                    hostname: 'Unknown',
                    status: 'Error',
                    isValid: false
                });
            }
        }

        console.log('Device check results:', deviceResults);

        // Process results
        const newDevices = deviceResults.map((device, index) => {
            const newDevice = {
                id: manualDevices.length + index + 1,
                name: device.hostname || 'Unknown',
                ip: device.ip,
                status: device.isValid ? 'Online' : 'Offline',
                isValid: device.isValid
            };

            // Add to global array if it doesn't already exist and is valid
            const existingDeviceIndex = allDiscoveredDevices.findIndex(d => d.ip === device.ip);
            if (existingDeviceIndex === -1 && device.isValid) {
                allDiscoveredDevices.push(newDevice);
                console.log(`Added device ${device.ip} to global array from manual entry`);
            } else if (existingDeviceIndex !== -1 && device.isValid) {
                // Update existing device if status changed
                allDiscoveredDevices[existingDeviceIndex] = newDevice;
                console.log(`Updated device ${device.ip} in global array from manual entry`);
            }

            return newDevice;
        });

        // Add to the list
        manualDevices = [...manualDevices, ...newDevices];

        // Store all discovered devices
        storeAllDiscoveredDevices();

        // Update device count
        if (manualDeviceCount) manualDeviceCount.textContent = manualDevices.length;

        // Add completion message
        const validDevices = deviceResults.filter(d => d.isValid).length;
        if (validDevices > 0) {
            addManualLog(`Check completed. Found ${validDevices} valid device(s).`, 'success');
            if (manualCheckStatus) {
                manualCheckStatus.textContent = 'Completed';
                manualCheckStatus.className = 'badge bg-success';
            }
        } else {
            addManualLog('Check completed. No valid devices found.', 'warning');
            if (manualCheckStatus) {
                manualCheckStatus.textContent = 'No Devices';
                manualCheckStatus.className = 'badge bg-warning text-dark';
            }
        }

        // Display the results
        if (manualResults) manualResults.style.display = 'block';
        renderDeviceTable(manualDevices, manualDeviceTableBody);

        // Clear the input
        manualIpInput.value = '';

        // Show continue button
        updateContinueButton();
    } catch (error) {
        console.error('Error checking devices:', error);

        // Add error message to log
        addManualLog(`Error checking devices: ${error.message}`, 'error');

        if (manualCheckStatus) {
            manualCheckStatus.textContent = 'Error';
            manualCheckStatus.className = 'badge bg-danger';
        }

        alert(`Error checking devices: ${error.message}`);
    } finally {
        // Re-enable button
        if (addManualIpButton) {
            addManualIpButton.disabled = false;
            addManualIpButton.innerHTML = '<i class="bi bi-plus-circle me-2"></i>Add';
        }
    }
}

// Render device table
function renderDeviceTable(devices, tableBody) {
    if (!tableBody) {
        console.error('tableBody is null or undefined');
        return;
    }

    tableBody.innerHTML = '';

    if (devices.length === 0) {
        const emptyRow = document.createElement('tr');
        emptyRow.innerHTML = `
            <td colspan="4" class="text-center py-4">
                <i class="bi bi-info-circle text-info fs-3 d-block mb-2"></i>
                No devices found
            </td>
        `;
        tableBody.appendChild(emptyRow);
        return;
    }

    devices.forEach(device => {
        const row = document.createElement('tr');

        // Always enable checkboxes for all devices, regardless of status
        row.innerHTML = `
            <td>
                <div class="form-check">
                    <input class="form-check-input device-checkbox" type="checkbox" value="${device.id}">
                </div>
            </td>
            <td>${device.name}</td>
            <td>${device.ip}</td>
            <td>
                <span class="badge ${device.status === 'Online' || device.isValid ? 'bg-success' : 'bg-danger'}">
                    ${device.status}
                </span>
            </td>
        `;

        tableBody.appendChild(row);
    });

    // Add event listeners to checkboxes with a more direct approach
    const checkboxes = tableBody.querySelectorAll('.device-checkbox');
    console.log(`Adding event listeners to ${checkboxes.length} checkboxes`);

    checkboxes.forEach(checkbox => {
        // Use addEventListener instead of replacing the element
        checkbox.addEventListener('change', function() {
            console.log('Checkbox changed, value:', this.value, 'checked:', this.checked);
            // Force update the continue button immediately
            updateContinueButton();
        });
    });

    // Force an update of the continue button
    updateContinueButton();
}

// Update continue button visibility
function updateContinueButton() {
    console.log('Updating continue button...');

    // Get references to elements directly to ensure we have the latest
    const continueButtonContainer = document.getElementById('continueButtonContainer');
    const selectedDevicesCount = document.getElementById('selectedDevicesCount');
    const continueButton = document.getElementById('continueButton');

    if (!continueButtonContainer || !selectedDevicesCount || !continueButton) {
        console.error('Required elements not found:',
            !continueButtonContainer ? 'continueButtonContainer missing' : '',
            !selectedDevicesCount ? 'selectedDevicesCount missing' : '',
            !continueButton ? 'continueButton missing' : '');
        return;
    }

    // Determine which tab is active
    const tableBody = activeTab === 'scan' ? deviceTableBody : manualDeviceTableBody;

    if (!tableBody) {
        console.error('tableBody is null or undefined');
        return;
    }

    // Check if any devices are selected
    const selectedCheckboxes = Array.from(tableBody.querySelectorAll('.device-checkbox:checked'));
    const selectedCount = selectedCheckboxes.length;

    console.log(`Found ${selectedCount} selected checkboxes`);

    // Update the selected devices count
    selectedDevicesCount.textContent = `${selectedCount} device${selectedCount !== 1 ? 's' : ''} selected`;
    console.log(`Updated selectedDevicesCount to: ${selectedCount} device${selectedCount !== 1 ? 's' : ''} selected`);

    // Enable/disable continue button based on selection
    console.log('Updating continue button state, selectedCount:', selectedCount);

    if (selectedCount > 0) {
        // Enable the button
        continueButton.disabled = false;
        console.log('Enabling continue button - set disabled=false');
    } else {
        // Disable the button
        continueButton.disabled = true;
        console.log('Disabling continue button - set disabled=true');
    }

    // Always show continue button if devices are found, even if none are selected
    if ((activeTab === 'scan' && discoveredDevices.length > 0) ||
        (activeTab === 'manual' && manualDevices.length > 0)) {
        continueButtonContainer.style.display = 'block';
        console.log('Showing continue button container');
    } else {
        continueButtonContainer.style.display = 'none';
        console.log('Hiding continue button container');
    }
}

// Proceed to the next page
function proceedToNextPage() {
    console.log('Proceeding to next page...');

    // Just navigate directly to index.html
    window.location.href = 'index.html';
}

// Initialize the UI when the DOM is loaded
document.addEventListener('DOMContentLoaded', initUI);

// Also initialize when the window loads (as a fallback)
window.addEventListener('load', () => {
    console.log('Window loaded, initializing UI...');
    initUI();
});

// Add keyboard shortcut for page refresh (Ctrl+R)
document.addEventListener('keydown', function(event) {
    // Check if Ctrl+R was pressed (key 'r' or 'R')
    if ((event.ctrlKey || event.metaKey) && (event.key === 'r' || event.key === 'R')) {
        console.log('Ctrl+R keyboard shortcut detected');

        // Prevent the default browser refresh behavior
        event.preventDefault();

        // Add a log message
        addTerminalLog('Refreshing page via keyboard shortcut (Ctrl+R)...', 'info');

        // Show loading state on the refresh button if it exists
        const refreshButton = document.getElementById('refreshButton');
        if (refreshButton) {
            refreshButton.disabled = true;
            refreshButton.innerHTML = '<span class="spinner-border spinner-border-sm me-2"></span>Refreshing...';
        }

        // Reload the page after a short delay
        setTimeout(() => {
            window.location.reload();
        }, 500);
    }
});
