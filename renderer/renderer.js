
function getFilenameFromPath(path) {
    if (!path) return '';
    // Split by forward slash and backslash to handle both Unix and Windows paths
    const parts = path.split(/[\/\\]/);
    return parts[parts.length - 1];
}

// Debug function to check if elements exist
function checkElements() {
    console.log('Checking UI elements:');
    console.log('deviceSelect:', deviceSelect ? 'Found' : 'Not found');
    console.log('filePathInput:', filePathInput ? 'Found' : 'Not found');
    console.log('filePathDisplay:', filePathDisplay ? 'Found' : 'Not found');
    console.log('firmwareBox:', firmwareBox ? 'Found' : 'Not found');
    console.log('flashButton:', flashButton ? 'Found' : 'Not found');
    console.log('statusDiv:', statusDiv ? 'Found' : 'Not found');
    console.log('statusCard:', statusCard ? 'Found' : 'Not found');
    console.log('statusBadge:', statusBadge ? 'Found' : 'Not found');
    console.log('browseButton:', browseButton ? 'Found' : 'Not found');
    console.log('backButton:', backButton ? 'Found' : 'Not found');
    console.log('selectedDevicesTableBody:', selectedDevicesTableBody ? 'Found' : 'Not found');
    console.log('selectAllDevices:', selectAllDevices ? 'Found' : 'Not found');
}

// Initialize UI elements
const deviceSelect = document.getElementById('deviceTypeSelect');
const filePathInput = document.getElementById('filePathInput');
const filePathDisplay = document.getElementById('filePathDisplay');
const firmwareBox = document.getElementById('firmwareBox');
const flashButton = firmwareBox ? firmwareBox.querySelector('button') : null;
const statusDiv = document.getElementById('status');
const statusCard = document.getElementById('statusCard');
const statusBadge = document.getElementById('statusBadge');
const browseButton = document.getElementById('browseButton');
const backButton = document.getElementById('backButton');
const selectedDevicesTableBody = document.getElementById('selectedDevicesTableBody');
const selectAllDevices = document.getElementById('selectAllDevices');
const showLogButton = document.getElementById('showLogButton');
const debugButton = document.getElementById('debugButton');

// Check if selectedDevicesTableBody exists
if (!selectedDevicesTableBody) {
    console.error('CRITICAL ERROR: selectedDevicesTableBody element not found!');
    // This is a critical error that will prevent the page from working properly
    alert('Error: Could not find the device table. Please refresh the page or restart the application.');
}

// Log console elements
const operationLogConsole = document.getElementById('operationLogConsole');
const operationLogContent = document.getElementById('operationLogContent');
const toggleOperationLog = document.getElementById('toggleOperationLog');

// Check if all UI elements are properly initialized
console.log('Page loaded, checking UI elements...');
checkElements();

let selectedDeviceType = null;
let selectedFilePath = null;
let discoveredDevices = [];

// Add keyboard shortcut for page refresh (Ctrl+R)
document.addEventListener('keydown', function(event) {
    // Check if Ctrl+R was pressed (key 'r' or 'R')
    if ((event.ctrlKey || event.metaKey) && (event.key === 'r' || event.key === 'R')) {
        console.log('Ctrl+R keyboard shortcut detected');

        // Prevent the default browser refresh behavior
        event.preventDefault();

        // Add a log message if the function exists
        if (typeof appendStatus === 'function') {
            appendStatus('Refreshing page via keyboard shortcut (Ctrl+R)...', 'info');
        }

        // Reload the page after a short delay
        setTimeout(() => {
            window.location.reload();
        }, 500);
    }
});

// Function to directly populate the device table
function populateDeviceTable(devices) {
    console.log('Populating device table with devices:', devices);

    if (!selectedDevicesTableBody) {
        console.error('Cannot populate table: selectedDevicesTableBody not found');
        return false;
    }

    if (!devices || devices.length === 0) {
        console.warn('No devices to populate table with');
        selectedDevicesTableBody.innerHTML = `
            <tr>
                <td colspan="4" class="text-center py-4">
                    <i class="bi bi-exclamation-circle text-warning fs-3 d-block mb-2"></i>
                    No devices found. Please go back to device discovery page.
                </td>
            </tr>
        `;
        return false;
    }

    // Clear existing rows
    selectedDevicesTableBody.innerHTML = '';

    // Add each device to the table
    devices.forEach(device => {
        const row = document.createElement('tr');
        row.innerHTML = `
            <td>
                <div class="form-check">
                    <input class="form-check-input device-checkbox" type="checkbox" value="${device.id}" checked>
                </div>
            </td>
            <td>${device.name || 'Unknown'}</td>
            <td>${device.ip}</td>
            <td><span class="badge bg-success">Ready</span></td>
        `;
        selectedDevicesTableBody.appendChild(row);
    });

    console.log(`Added ${devices.length} devices to the table`);

    // Set up select all checkbox
    if (selectAllDevices) {
        selectAllDevices.checked = true;
        selectAllDevices.addEventListener('change', (e) => {
            const checkboxes = selectedDevicesTableBody.querySelectorAll('.device-checkbox');
            checkboxes.forEach(checkbox => {
                checkbox.checked = e.target.checked;
            });
        });
    }

    return true;
}



// Initialize the page
document.addEventListener('DOMContentLoaded', () => {
    console.log('DOM content loaded, initializing operation log console...');

    try {
        // Try to get devices from multiple storage locations
        let selectedDevicesJson = localStorage.getItem('selectedDevices');

        // If not in localStorage, try sessionStorage
        if (!selectedDevicesJson) {
            console.log('No data in localStorage, checking sessionStorage...');
            selectedDevicesJson = sessionStorage.getItem('selectedDevices');
        }

        // If still not found, try validDiscoveredDevices
        if (!selectedDevicesJson) {
            console.log('No data in sessionStorage, checking validDiscoveredDevices...');
            selectedDevicesJson = localStorage.getItem('validDiscoveredDevices') ||
                                 sessionStorage.getItem('validDiscoveredDevices');
        }

        // If still not found, try allDiscoveredDevices
        if (!selectedDevicesJson) {
            console.log('No data in validDiscoveredDevices, checking allDiscoveredDevices...');
            const allDevicesJson = localStorage.getItem('allDiscoveredDevices') ||
                                  sessionStorage.getItem('allDiscoveredDevices');

            if (allDevicesJson) {
                // Parse and filter for valid devices only
                const allDevices = JSON.parse(allDevicesJson);
                const validDevices = allDevices.filter(device => device.isValid);

                if (validDevices.length > 0) {
                    selectedDevicesJson = JSON.stringify(validDevices);
                    console.log(`Found ${validDevices.length} valid devices in allDiscoveredDevices`);
                }
            }
        }

        if (selectedDevicesJson) {
            const selectedDevices = JSON.parse(selectedDevicesJson);

            if (selectedDevices && selectedDevices.length > 0) {
                // Store the devices for later use
                discoveredDevices = selectedDevices;

                // Show a message
                if (statusDiv) {
                    statusCard.style.display = 'block';
                    statusBadge.className = 'badge bg-success';
                    statusBadge.textContent = 'Ready';

                    appendStatus(`Loaded ${selectedDevices.length} selected devices from discovery page`, 'success');

                    // Display device information in the status
                    appendStatus('Selected devices:');
                    selectedDevices.forEach(device => {
                        appendStatus(`- Device: ${device.name || 'Unknown'}, IP: ${device.ip}, Status: ${device.status || 'Ready'}`);
                    });
                }

                // Populate the selected devices table
                if (selectedDevicesTableBody) {
                    selectedDevicesTableBody.innerHTML = '';

                    selectedDevices.forEach(device => {
                        const row = document.createElement('tr');
                        row.innerHTML = `
                            <td>
                                <div class="form-check">
                                    <input class="form-check-input device-checkbox" type="checkbox" value="${device.id}" checked>
                                </div>
                            </td>
                            <td>${device.name || 'Unknown'}</td>
                            <td>${device.ip}</td>
                            <td><span class="badge bg-success">Ready</span></td>
                        `;
                        selectedDevicesTableBody.appendChild(row);
                    });

                    // Set up select all checkbox
                    if (selectAllDevices) {
                        selectAllDevices.addEventListener('change', (e) => {
                            const checkboxes = selectedDevicesTableBody.querySelectorAll('.device-checkbox');
                            checkboxes.forEach(checkbox => {
                                checkbox.checked = e.target.checked;
                            });
                        });
                    }
                }

                // Don't clear storage immediately to allow for page refreshes
                console.log('Keeping device data in storage for page refreshes');
            } else {
                // No devices selected, show warning
                if (statusDiv) {
                    statusCard.style.display = 'block';
                    statusBadge.className = 'badge bg-warning text-dark';
                    statusBadge.textContent = 'Warning';
                    appendStatus('Warning: No devices were selected. Please go back and select at least one device.', 'warning');
                }

                // Show empty table message
                if (selectedDevicesTableBody) {
                    selectedDevicesTableBody.innerHTML = `
                        <tr>
                            <td colspan="4" class="text-center py-4">
                                <i class="bi bi-exclamation-circle text-warning fs-3 d-block mb-2"></i>
                                No devices selected. Please go back to select devices.
                            </td>
                        </tr>
                    `;
                }
            }
        } else {
            // No devices in storage, show warning
            if (statusDiv) {
                statusCard.style.display = 'block';
                statusBadge.className = 'badge bg-warning text-dark';
                statusBadge.textContent = 'Warning';
                appendStatus('Warning: No devices were selected. Please go back and select at least one device.', 'warning');
            }

            // Show empty table message
            if (selectedDevicesTableBody) {
                selectedDevicesTableBody.innerHTML = `
                    <tr>
                        <td colspan="4" class="text-center py-4">
                            <i class="bi bi-exclamation-circle text-warning fs-3 d-block mb-2"></i>
                            No devices selected. Please go back to select devices.
                        </td>
                    </tr>
                `;
            }
        }
    } catch (error) {
        console.error('Error loading selected devices:', error);
        if (statusDiv) {
            statusCard.style.display = 'block';
            statusBadge.className = 'badge bg-danger';
            statusBadge.textContent = 'Error';
            appendStatus(`Error loading selected devices: ${error.message}`, 'error');
        }
    }

    // Initialize operation log console
    if (operationLogConsole && toggleOperationLog) {
        console.log('Initializing operation log console');

        // Add a test message to the log console
        appendStatus('Firmware flasher initialized successfully', 'success');
        appendStatus('Ready to select devices and firmware', 'info');

        // Set up toggle button for operation log console
        toggleOperationLog.addEventListener('click', () => {
            if (!operationLogConsole) return;

            if (operationLogConsole.classList.contains('collapsed')) {
                // Expand
                operationLogConsole.classList.remove('collapsed');
                toggleOperationLog.innerHTML = '<i class="bi bi-chevron-down"></i>';
            } else {
                // Collapse
                operationLogConsole.classList.add('collapsed');
                toggleOperationLog.innerHTML = '<i class="bi bi-chevron-up"></i>';
            }
        });
    }

    try {
        console.log('Attempting to load selected devices...');

        // First, try to get specifically selected devices
        let selectedDevicesJson = localStorage.getItem('selectedDevices');

        // If not in localStorage, try sessionStorage
        if (!selectedDevicesJson) {
            console.log('No selected devices in localStorage, checking sessionStorage...');
            selectedDevicesJson = sessionStorage.getItem('selectedDevices');
        }

        // If still not found, look for backup entries
        if (!selectedDevicesJson) {
            console.log('No selected devices in sessionStorage, checking for backup entries...');
            // Find the most recent backup entry
            let latestTimestamp = 0;
            let latestKey = null;

            for (let i = 0; i < localStorage.length; i++) {
                const key = localStorage.key(i);
                if (key.startsWith('selectedDevices_')) {
                    const timestamp = parseInt(key.split('_')[1]);
                    if (timestamp > latestTimestamp) {
                        latestTimestamp = timestamp;
                        latestKey = key;
                    }
                }
            }

            if (latestKey) {
                console.log(`Found backup entry: ${latestKey}`);
                selectedDevicesJson = localStorage.getItem(latestKey);
            }
        }

        // If still no selected devices, try to get all discovered devices
        if (!selectedDevicesJson) {
            console.log('No specifically selected devices found, checking for valid discovered devices...');

            // First try to get valid devices only (better for the second page)
            let validDevicesJson = sessionStorage.getItem('validDiscoveredDevices');

            if (!validDevicesJson) {
                validDevicesJson = localStorage.getItem('validDiscoveredDevices');
            }

            if (validDevicesJson) {
                console.log('Found valid discovered devices, using these instead of selected devices');
                selectedDevicesJson = validDevicesJson;
            } else {
                console.log('No valid devices found, checking for all discovered devices...');

                // Try to get from sessionStorage first (more reliable between page navigations)
                let allDevicesJson = sessionStorage.getItem('allDiscoveredDevices');

                // If not in sessionStorage, try localStorage
                if (!allDevicesJson) {
                    allDevicesJson = localStorage.getItem('allDiscoveredDevices');
                }

                if (allDevicesJson) {
                    console.log('Found all discovered devices, filtering valid ones');
                    try {
                        // Parse the JSON and filter for valid devices only
                        const allDevices = JSON.parse(allDevicesJson);
                        const validDevices = allDevices.filter(device => device.isValid);

                        if (validDevices.length > 0) {
                            console.log(`Filtered ${validDevices.length} valid devices from ${allDevices.length} total`);
                            selectedDevicesJson = JSON.stringify(validDevices);
                        } else {
                            console.log('No valid devices found in allDiscoveredDevices');
                            selectedDevicesJson = allDevicesJson; // Use all as fallback
                        }
                    } catch (e) {
                        console.error('Error parsing/filtering devices:', e);
                        selectedDevicesJson = allDevicesJson; // Use all as fallback
                    }
                }
            }
        }

        console.log('Retrieved data:', selectedDevicesJson);

        // Instead of hardcoding, we'll check if we need to redirect back to discovery
        if (!selectedDevicesJson) {
            console.log('No device data found in any storage');

            // Show a notification that we need to go back to device discovery
            if (operationLogConsole) {
                operationLogConsole.style.display = 'block';
                appendStatus('No device data found. Please go back to device discovery and select devices.', 'warning');
            }

            // After a short delay, redirect back to device discovery
            setTimeout(() => {
                console.log('Redirecting back to device discovery page');
                window.location.href = 'device-discovery.html';
            }, 3000);

            return; // Exit early
        }

        if (selectedDevicesJson) {
            const selectedDevices = JSON.parse(selectedDevicesJson);
            console.log('Parsed selected devices:', selectedDevices);

            if (selectedDevices && selectedDevices.length > 0) {
                // Store the devices for later use
                discoveredDevices = selectedDevices;
                console.log('Stored devices in discoveredDevices array:', discoveredDevices);

                // Show a message
                appendStatus(`Loaded ${selectedDevices.length} selected devices from discovery page`, 'success');

                // Display device information in the status
                appendStatus('Selected devices:');
                selectedDevices.forEach(device => {
                    appendStatus(`- Device: ${device.name}, IP: ${device.ip}, Status: ${device.status}`);
                });

                // Populate the selected devices table using our direct function
                const populationResult = populateDeviceTable(selectedDevices);
                console.log('Table population result:', populationResult ? 'Success' : 'Failed');
            } else {
                console.warn('No devices in the parsed data');
                // No devices selected, show warning
                appendStatus('Warning: No devices were selected. Please go back and select at least one device.', 'warning');

                // Show empty table message
                if (selectedDevicesTableBody) {
                    selectedDevicesTableBody.innerHTML = `
                        <tr>
                            <td colspan="4" class="text-center py-4">
                                <i class="bi bi-exclamation-circle text-warning fs-3 d-block mb-2"></i>
                                No devices selected. Please go back to select devices.
                            </td>
                        </tr>
                    `;
                }
            }

            // Don't clear storage at all - we need it for page refreshes
            console.log('Keeping device data in storage for page refreshes');
        } else {
            console.warn('No selected devices data found in any storage');
            // No devices in storage, show warning
            appendStatus('Warning: No devices were selected. Please go back and select at least one device.', 'warning');

            // Show empty table message
            if (selectedDevicesTableBody) {
                selectedDevicesTableBody.innerHTML = `
                    <tr>
                        <td colspan="4" class="text-center py-4">
                            <i class="bi bi-exclamation-circle text-warning fs-3 d-block mb-2"></i>
                            No devices selected. Please go back to select devices.
                        </td>
                    </tr>
                `;
            }
        }
    } catch (error) {
        console.error('Error loading selected devices:', error);
        appendStatus(`Error loading selected devices: ${error.message}`, 'error');
    }

    // Set up back button
    if (backButton) {
        backButton.addEventListener('click', () => {
            // Navigate back to the device discovery page
            window.location.href = 'device-discovery.html';
        });
    }
});


deviceSelect.addEventListener('change', () => {
    selectedDeviceType = deviceSelect.value === '1' ? 'device' : deviceSelect.value === '2' ? 'operator' : null;
    if (!selectedDeviceType) {
        // Reset UI elements
        firmwareBox.style.display = 'none';
        filePathInput.value = '';
        filePathDisplay.textContent = 'No file chosen';
        selectedFilePath = null;
    } else {
        // Clear file selection when device type changes
        filePathInput.value = '';
        filePathDisplay.textContent = 'No file chosen';
        selectedFilePath = null;
        firmwareBox.style.display = 'none';
    }
});


// Replace file input with a button that uses Electron's dialog
if (browseButton) {
    console.log('Adding click event listener to browseButton');

    browseButton.addEventListener('click', async () => {
        try {
            console.log('Browse button clicked');

            // Check if window.electronAPI exists
            if (!window.electronAPI) {
                console.error('window.electronAPI is not defined');
                alert('Error: electronAPI is not available. Please restart the application.');
                return;
            }

            // Show loading state
            browseButton.disabled = true;
            browseButton.innerHTML = '<span class="spinner-border spinner-border-sm me-2"></span>Browsing...';

            console.log('Calling window.electronAPI.selectFile()');
            const result = await window.electronAPI.selectFile();
            console.log('File selection result:', result);

            if (result.error) {
                console.error('Error selecting file:', result.error);
                if (typeof appendStatus === 'function') {
                    appendStatus(`Error selecting file: ${result.error}`, 'error');
                }
                return;
            }

            if (!result.canceled) {
                selectedFilePath = result.filePath;
                console.log('Selected file path:', selectedFilePath);

                // Update the UI to show the selected file path
                if (filePathInput) {
                    filePathInput.value = result.fileName || getFilenameFromPath(result.filePath);
                    console.log('Updated input value:', filePathInput.value);
                } else {
                    console.error('filePathInput element not found');
                }

                if (filePathDisplay) {
                    filePathDisplay.textContent = result.filePath;
                    console.log('Updated display text:', filePathDisplay.textContent);
                } else {
                    console.error('filePathDisplay element not found');
                }

                // Show the Flash Firmware button
                if (firmwareBox) {
                    firmwareBox.style.display = 'block';
                }

                // Show success message
                if (typeof appendStatus === 'function') {
                    appendStatus(`File selected: ${result.fileName || getFilenameFromPath(result.filePath)}`, 'success');
                }
            } else {
                console.log('File selection canceled');
            }
        } catch (error) {
            console.error('Error selecting file:', error);
            if (typeof appendStatus === 'function') {
                appendStatus(`Error selecting file: ${error.message}`, 'error');
            }
        } finally {
            // Reset button state
            if (browseButton) {
                browseButton.disabled = false;
                browseButton.innerHTML = '<i class="bi bi-folder2-open me-2"></i>Browse';
            }
        }
    });
} else {
    console.error('browseButton element not found');
}

// Add click event listener to flashButton if it exists
if (flashButton) {
    console.log('Adding click event listener to flashButton');

    flashButton.addEventListener('click', async () => {
        console.log('Flash Firmware button clicked');

        if (!selectedDeviceType) {
            appendStatus('Please select a device type first', 'warning');
            return;
        }

        if (!selectedFilePath) {
            appendStatus('Please select a firmware file first', 'warning');
            return;
        }

        if (!discoveredDevices || discoveredDevices.length === 0) {
            appendStatus('No devices selected. Please go back to the device discovery page and select at least one device.', 'warning');
            return;
        }

        // Check if window.electronAPI exists
        if (!window.electronAPI) {
            console.error('window.electronAPI is not defined');
            alert('Error: electronAPI is not available. Please restart the application.');
            return;
        }

        // Show status card and reset badge
        if (statusCard) statusCard.style.display = 'block';
        if (statusBadge) {
            statusBadge.className = 'badge bg-info text-dark';
            statusBadge.textContent = 'Running';
        }

        try {
            // Upload the firmware file to the selected device type
            appendStatus('Starting firmware upload...');
            appendStatus(`Selected file: ${selectedFilePath}`);

            // Upload the file using the actual file path
            console.log('Calling window.electronAPI.uploadFirmware()');
            const uploadResult = await window.electronAPI.uploadFirmware(selectedDeviceType, selectedFilePath);
            console.log('Upload result:', uploadResult);

            if (uploadResult.error) {
                appendStatus(uploadResult.error, 'error');
            } else {
                appendStatus(uploadResult.message, 'success');
                appendStatus(`Original file path: ${uploadResult.originalFilePath}`);
                appendStatus(`Saved to: ${uploadResult.savedPath}`);
            }

            // Flash the firmware on the selected device type
            appendStatus('Starting firmware flash...');
            console.log('Calling window.electronAPI.flashFirmware()');
            const flashResult = await window.electronAPI.flashFirmware([selectedDeviceType]);
            console.log('Flash result:', flashResult);
            appendStatus(flashResult);

            // Now activate OTA update with the selected device type and IPs
            appendStatus('Starting OTA activation...');

            // Get the IPs of the selected devices from the table
            const selectedDeviceIds = selectedDevicesTableBody ?
                Array.from(selectedDevicesTableBody.querySelectorAll('.device-checkbox:checked'))
                    .map(checkbox => parseInt(checkbox.value)) : [];

            // Filter the devices based on selected checkboxes
            const selectedDevices = discoveredDevices.filter(device => selectedDeviceIds.includes(device.id));
            const deviceIPs = selectedDevices.map(device => device.ip);

            let otaResult;

            if (deviceIPs.length === 0) {
                appendStatus('Warning: No devices selected in the table. Using all loaded devices.');
                // Fallback to all devices if none selected
                const allDeviceIPs = discoveredDevices.map(device => device.ip);
                appendStatus(`Activating OTA on all devices: ${allDeviceIPs.join(', ')}`);

                console.log('Calling window.electronAPI.activateOta() with all devices');
                otaResult = await window.electronAPI.activateOta({
                    selected: [selectedDeviceType],
                    deviceIPs: allDeviceIPs
                });
            } else {
                appendStatus(`Activating OTA on selected devices: ${deviceIPs.join(', ')}`);

                console.log('Calling window.electronAPI.activateOta() with selected devices');
                otaResult = await window.electronAPI.activateOta({
                    selected: [selectedDeviceType],
                    deviceIPs: deviceIPs
                });
            }
            console.log('OTA result:', otaResult);

            // Handle different response formats
            if (typeof otaResult === 'string') {
                // Simple success message
                appendStatus(otaResult, 'success');
                if (statusBadge) {
                    statusBadge.className = 'badge bg-success';
                    statusBadge.textContent = 'Completed';
                }
            } else if (otaResult && otaResult.status === 'error') {
                // Error with details
                appendStatus(`Error: ${otaResult.message}`, 'error');
                if (statusBadge) {
                    statusBadge.className = 'badge bg-danger';
                    statusBadge.textContent = 'Failed';
                }

                // Display detailed error information
                if (otaResult.details && otaResult.details.length > 0) {
                    appendStatus('Error details:', 'error');
                    otaResult.details.forEach(detail => {
                        appendStatus(`- ${detail}`, 'error');
                    });
                }
            } else {
                // Unknown response format
                appendStatus(JSON.stringify(otaResult), 'info');
            }
        } catch (error) {
            console.error('Error during firmware process:', error);
            appendStatus(`Error: ${error.message}`, 'error');
            if (statusBadge) {
                statusBadge.className = 'badge bg-danger';
                statusBadge.textContent = 'Failed';
            }
        }
    });
} else {
    console.error('flashButton element not found');
}

// Function to append status messages
function appendStatus(message, type = 'info') {
    // Log to console for debugging
    console.log(`[${type.toUpperCase()}] ${message}`);

    // Add to operation log console
    addToOperationLog(message, type);
}

// Function to add log message to operation log console
function addToOperationLog(message, type = 'info') {
    if (!operationLogConsole || !operationLogContent) {
        console.error('operationLogConsole or operationLogContent element not found');
        return;
    }

    // Show the log console if it's hidden
    operationLogConsole.style.display = 'block';

    // Create a new log entry
    const logEntry = document.createElement('div');

    // Format the timestamp
    const timestamp = new Date().toLocaleTimeString();

    // Set the appropriate class based on message type
    let className = '';
    switch(type) {
        case 'error':
            className = 'text-danger';
            break;
        case 'success':
            className = 'text-success';
            break;
        case 'warning':
            className = 'text-warning';
            break;
        default:
            className = 'text-info';
    }

    // Set the HTML content
    logEntry.innerHTML = `<span class="text-secondary">[${timestamp}]</span> <span class="${className}">${message}</span>`;

    // Add to the log content
    operationLogContent.appendChild(logEntry);

    // Scroll to the bottom
    operationLogConsole.scrollTop = operationLogConsole.scrollHeight;
}
