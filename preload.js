const { contextBridge, ipc<PERSON><PERSON><PERSON> } = require('electron');

// Set up event listeners for progress updates
ipcRenderer.on('scan-progress', (event, data) => {
    // This will be handled by the renderer process
    document.dispatchEvent(new CustomEvent('scan-progress', { detail: data }));
});

// Set up event listeners for IP scan status updates
ipc<PERSON>enderer.on('ip-scan-status', (event, data) => {
    // This will be handled by the renderer process
    document.dispatchEvent(new CustomEvent('ip-scan-status', { detail: data }));
});

// Log when preload.js is loaded
console.log('Preload script loaded');

// Expose Electron APIs to the renderer process
contextBridge.exposeInMainWorld('electronAPI', {
    // Original methods
    selectFile: () => {
        console.log('Renderer called selectFile()');
        return ipcRenderer.invoke('select-file');
    },
    uploadFirmware: (type, filePath) => ipcRenderer.invoke('upload-firmware', type, filePath),
    flashFirmware: (selected) => ipcRenderer.invoke('flash-firmware', selected),
    activateOta: (data) => ipcRenderer.invoke('activate-ota', data),

    // New device discovery methods
    scanDevices: (range) => ipcRenderer.invoke('scan-devices', range),
    checkDevice: (ip) => ipcRenderer.invoke('check-device', ip)
});




