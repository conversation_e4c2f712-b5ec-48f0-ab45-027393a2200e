.heading-box h1{
    background-color: rgb(83, 83, 214);
    border-radius:7px;
    color: white;
    font-weight: bold;
    font-size: 32px;
    padding:10px;
}

#page{
    margin-top:120px;
    border:1px rgb(207, 207, 207) solid;
    border-radius: 8px;
    box-shadow: rgba(50, 50, 93, 0.25) 0px 2px 5px -1px, rgba(0, 0, 0, 0.3) 0px 1px 3px -1px;
}
.dropdown {
    margin-bottom: 100px;
  }

#firmwareBox {
    border: 1px solid #86b7fe;
    border-radius: 8px;
    background-color: #f8f9fa;
    margin-top: 20px;
    margin-bottom:10px;
    box-shadow: 0 0 0 0.2rem rgba(13, 110, 253, 0.25);
    cursor: pointer;
    transition: background-color 0.3s ease;
}

#firmwareBox:hover {
    background-color: #e2e6ea;
}

@media (max-width: 767px) {
    .container {
      padding-left: 10px;
      padding-right: 10px;
    }
    .dropdown {
        margin-bottom: 0px;
      }
      .heading-box h1{
        background-color: rgb(83, 83, 214);
        border-radius:7px;
        color: white;
        font-weight: bold;
        padding:10px;
    }
    
  }
