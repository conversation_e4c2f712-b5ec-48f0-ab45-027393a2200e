const { app, BrowserWindow, ipcMain, dialog } = require('electron');
const path = require('path');
const { exec } = require('child_process');
const fs = require('fs');

let selectedFirmwareFile = null;
let selectedDeviceType = null;
let firmwareDir = null;

// firmware directory path
const FIRMWARE_DIR = path.join(__dirname, 'firmware');

function createWindow() {
    // Get the primary display's dimensions
    const { screen, Menu } = require('electron');
    const primaryDisplay = screen.getPrimaryDisplay();
    const { width, height } = primaryDisplay.workAreaSize;

    // Calculate window size based on screen size
    // Use 80% of screen width and height for the window
    const windowWidth = Math.floor(width * 0.8);
    const windowHeight = Math.floor(height * 0.8);

    // Create a responsive window
    const win = new BrowserWindow({
        width: windowWidth,
        height: windowHeight,
        minWidth: 800,  // Set minimum width
        minHeight: 600, // Set minimum height
        center: true,   // Center the window on the screen
        webPreferences: {
            preload: path.join(__dirname, 'preload.js'),
            nodeIntegration: false,
            contextIsolation: true,
        },
        // Make the window look modern
        frame: true,
        titleBarStyle: 'default',
        backgroundColor: '#1e1e1e',
    });

    // Remove the menu bar
    Menu.setApplicationMenu(null);

    // Load the device discovery HTML file as the starting page
    win.loadFile('renderer/device-discovery.html');

    // Log screen and window dimensions for debugging
    console.log(`Screen dimensions: ${width}x${height}`);
    console.log(`Window dimensions: ${windowWidth}x${windowHeight}`);

    // Create firmware directory if it doesn't exist
    if (!fs.existsSync(FIRMWARE_DIR)) {
        fs.mkdirSync(FIRMWARE_DIR, { recursive: true });
    }

    // Handle window resize events
    win.on('resize', () => {
        const [newWidth, newHeight] = win.getSize();
        console.log(`Window resized to: ${newWidth}x${newHeight}`);
    });
}

app.whenReady().then(createWindow);


// Function to test if an IP is reachable with a short timeout
const testConnection = (ip, user, password, timeout = 2, event = null) => {
    return new Promise((resolve) => {
        // Use ping first for faster checking
        const pingCmd = `ping -c 1 -W ${timeout} ${ip}`;

        exec(pingCmd, (pingError) => {
            if (pingError) {
                // If ping fails, the host is likely not reachable
                console.log(`IP ${ip} is not reachable (ping failed)`);

                // Send status to renderer if event is provided
                if (event) {
                    event.sender.send('ip-scan-status', {
                        ip,
                        status: 'error',
                        message: 'Not reachable (ping failed)'
                    });
                }

                resolve(false);
            } else {
                // If ping succeeds, try SSH
                const testCmd = `timeout ${timeout} sshpass -p '${password}' ssh -o ConnectTimeout=${timeout} -o StrictHostKeyChecking=no ${user}@${ip} 'echo Connection successful'`;
                console.log(`Testing SSH connection to ${ip}...`);

                // Send status to renderer if event is provided
                if (event) {
                    event.sender.send('ip-scan-status', {
                        ip,
                        status: 'info',
                        message: 'Reachable, testing SSH connection...'
                    });
                }

                exec(testCmd, (sshError) => {
                    if (sshError) {
                        console.log(`IP ${ip} is reachable but SSH failed`);

                        // Send status to renderer if event is provided
                        if (event) {
                            event.sender.send('ip-scan-status', {
                                ip,
                                status: 'warning',
                                message: 'Reachable but SSH failed'
                            });
                        }

                        resolve(false);
                    } else {
                        console.log(`IP ${ip} is reachable via SSH`);

                        // Send status to renderer if event is provided
                        if (event) {
                            event.sender.send('ip-scan-status', {
                                ip,
                                status: 'success',
                                message: 'Reachable via SSH'
                            });
                        }

                        resolve(true);
                    }
                });
            }
        });
    });
};

// Function to get hostname of a device
const getHostname = (ip, user, password, timeout = 3) => {
    return new Promise((resolve) => {
        const cmd = `timeout ${timeout} sshpass -p '${password}' ssh -o ConnectTimeout=${timeout} -o StrictHostKeyChecking=no ${user}@${ip} 'hostname'`;

        exec(cmd, (error, stdout, stderr) => {
            if (error) {
                console.log(`Could not get hostname for ${ip}: ${error.message}`);
                resolve(null);
            } else {
                const hostname = stdout.trim();
                console.log(`Hostname for ${ip}: ${hostname}`);
                resolve(hostname);
            }
        });
    });
};

const execCommand = (command, timeout = 15) => {
    // Extract IP from command for better logging
    const ipMatch = command.match(/\d+\.\d+\.\d+\.\d+/);
    const ipInfo = ipMatch ? `[IP ${ipMatch[0]}]` : '';
    const ip = ipMatch ? ipMatch[0] : 'unknown';

    // Add timeout to SSH commands if not already present
    let cmdWithTimeout = command;
    if (command.includes('ssh') && !command.includes('ConnectTimeout')) {
        cmdWithTimeout = command.replace('ssh', `ssh -o ConnectTimeout=${timeout}`);
    }
    if (command.includes('scp') && !command.includes('ConnectTimeout')) {
        cmdWithTimeout = command.replace('scp', `scp -o ConnectTimeout=${timeout}`);
    }

    // Record start time
    const startTime = Date.now();
    console.log(`${ipInfo} Starting execution at ${new Date(startTime).toLocaleTimeString()}`);
    console.log(`${ipInfo} Executing: ${cmdWithTimeout}`);

    return new Promise((resolve, reject) => {
        const process = exec(cmdWithTimeout);

        let output = '';

        // Set a timeout to kill the process if it takes too long
        const timeoutId = setTimeout(() => {
            process.kill();
            const executionTime = (Date.now() - startTime) / 1000;
            console.error(`${ipInfo} Command timed out after ${executionTime.toFixed(2)} seconds`);
            reject(new Error('Command timed out'));
        }, timeout * 1000);

        process.stdout.on('data', (data) => {
            const dataStr = data.toString();
            // Only log if it contains meaningful information
            if (dataStr.trim()) {
                console.log(`${ipInfo} Output: ${dataStr.trim()}`);
            }
            output += dataStr;
        });

        process.stderr.on('data', (data) => {
            const dataStr = data.toString();
            if (dataStr.trim()) {
                console.error(`${ipInfo} Error: ${dataStr.trim()}`);
            }
            output += dataStr;
        });

        process.on('close', (code) => {
            // Clear the timeout
            clearTimeout(timeoutId);

            // Calculate execution time
            const endTime = Date.now();
            const executionTime = (endTime - startTime) / 1000; // Convert to seconds

            if (code === 0) {
                console.log(`${ipInfo} Command completed successfully in ${executionTime.toFixed(2)} seconds`);
                resolve({ output, executionTime, ip });
            } else {
                const errorMsg = `Command exited with code ${code}`;
                console.error(`${ipInfo} ${errorMsg} after ${executionTime.toFixed(2)} seconds`);
                reject(new Error(errorMsg));
            }
        });
    });
};

ipcMain.handle('select-file', async (event) => {
    try {
        console.log('Select file dialog requested');
        const result = await dialog.showOpenDialog({
            title: 'Select Firmware File',
            properties: ['openFile'],
            filters: [
                { name: 'Firmware Files', extensions: ['bin', 'hex', 'elf'] },
                { name: 'All Files', extensions: ['*'] }
            ]
        });

        console.log('Dialog result:', result);

        // Check if dialog was canceled or no file was selected
        if (result.canceled || !result.filePaths || result.filePaths.length === 0) {
            console.log('File selection canceled or no file selected');
            return { canceled: true };
        }

        const filePath = result.filePaths[0];
        const fileName = path.basename(filePath);
        console.log(`File selected: ${fileName} (${filePath})`);

        return {
            canceled: false,
            filePath: filePath,
            fileName: fileName
        };
    } catch (error) {
        console.error('Error selecting file:', error);
        return { error: error.message };
    }
});

ipcMain.handle('upload-firmware', async (event, deviceType, filePath) => {
    try {
        selectedDeviceType = deviceType;

        // Validate the file path
        if (!filePath || !fs.existsSync(filePath)) {
            throw new Error('Invalid file path or file does not exist');
        }

        // Get the original file name from the path
        const originalFileName = path.basename(filePath);

        // Create a directory for the selected device type
        firmwareDir = path.join(FIRMWARE_DIR, selectedDeviceType);
        if (!fs.existsSync(firmwareDir)) {
            fs.mkdirSync(firmwareDir, { recursive: true });
        }

        // Use the original file name for the destination
        const destPath = path.join(firmwareDir, originalFileName);
        selectedFirmwareFile = destPath;

        // Copy the actual file from the source to the destination
        fs.copyFileSync(filePath, destPath);

        // Log the actual file path
        console.log(`Original file selected: ${filePath}`);
        // console.log(`${selectedDeviceType} firmware copied to: ${destPath}`);

        // Return both the original file path and the destination path
        return {
            message: `${selectedDeviceType.charAt(0).toUpperCase() + selectedDeviceType.slice(1)} firmware uploaded successfully`,
            originalFilePath: filePath,
            savedPath: destPath
        };
    } catch (error) {
        console.error('Error uploading firmware:', error);
        return {
            error: `Error uploading firmware: ${error.message}`
        };
    }
});

ipcMain.handle('activate-ota', async (event, data) => {
    console.log('Starting OTA activation...');
    const selected = (data && data.selected) || [];
    console.log('Selected options:', selected);

    const deviceUser = 'smartdockai';
    const devicePassword = 'Sach@151084!';
    const dbPassword = 'Sach@1510!';
    const operatorUser = 'smartdockop';
    const operatorPassword = 'Shiv@2703!';

    // Use provided device IPs if available, otherwise use default sequence
    let ips = [];
    if (data && data.deviceIPs && Array.isArray(data.deviceIPs) && data.deviceIPs.length > 0) {
        ips = data.deviceIPs;
        console.log('Using provided device IPs:', ips);
    } else {
        // Define IPs in sequence: 29.2, 29.4, 29.6, ..., 29.20
        for (let i = 2; i <= 20; i += 2) {
            ips.push(`192.168.29.${i}`);
        }
        console.log('Using default IPs:', ips);
    }

    // Track errors across all commands
    let hasErrors = false;
    let errorSummary = [];
    let successCount = 0;
    let totalCommands = 0;

    try {
        if (selected.includes('device') && selectedFirmwareFile && selectedDeviceType === 'device') {
            console.log('Processing device firmware...');
            const timestamp = new Date().toISOString().replace(/[-T:.Z]/g, '').slice(0, 14);
            const deviceFirmwarePath = path.join(firmwareDir, path.basename(selectedFirmwareFile));
            // console.log('Device firmware path:', deviceFirmwarePath);

            const deviceCommands = [
                {
                    cmd: (ip) => `sshpass -p '${devicePassword}' ssh -o StrictHostKeyChecking=no ${deviceUser}@${ip} 'echo ${devicePassword} | sudo -S systemctl stop svdgs.service && exit'`,
                    desc: 'Stopping svdgs.service'
                },
                {
                    cmd: (ip) => `sshpass -p '${devicePassword}' scp -o StrictHostKeyChecking=no ${deviceFirmwarePath} ${deviceUser}@${ip}:~`,
                    desc: 'Copying firmware file'
                },
                {
                    cmd: (ip) => `sshpass -p '${devicePassword}' ssh -o StrictHostKeyChecking=no ${deviceUser}@${ip} 'echo ${devicePassword} | sudo -S cp UI_VDGS /mnt/SoftwareBackup && exit'`,
                    desc: 'Backing up existing firmware'
                },
                {
                    cmd: (ip) => `sshpass -p '${devicePassword}' ssh -o StrictHostKeyChecking=no ${deviceUser}@${ip} 'echo ${devicePassword} | sudo -S bash -c "if [ -f /root/UI_VDGS ]; then mv /root/UI_VDGS /root/UI_VDGS_${timestamp}; fi && mv UI_VDGS /root && chmod +x /root/UI_VDGS"'`,
                    desc: 'Moving and setting permissions'
                },
                {
                    cmd: (ip) => `sshpass -p '${devicePassword}' ssh -o StrictHostKeyChecking=no ${deviceUser}@${ip} 'echo ${devicePassword} | sudo -S su -c "sha256sum /root/UI_VDGS > /root/UI_VDGS.sha256"'`,
                    desc: 'Generating SHA256'
                },
                {
                    cmd: (ip) => `sshpass -p '${devicePassword}' ssh -o StrictHostKeyChecking=no ${deviceUser}@${ip} 'echo ${devicePassword} | sudo -S mysql -u root -p${dbPassword} -e "USE Inxee; TRUNCATE TABLE FlightInfo;" && exit'`,
                    desc: 'Clearing FlightInfo table'
                },
                {
                    cmd: (ip) => `sshpass -p '${devicePassword}' ssh -o StrictHostKeyChecking=no ${deviceUser}@${ip} 'echo ${devicePassword} | sudo -S systemctl start svdgs.service && exit'`,
                    desc: 'Starting svdgs.service'
                }
            ];

            // Execute each command on all IPs sequentially before moving to the next command
            for (const command of deviceCommands) {
                totalCommands++;

                // Record command start time
                const commandStartTime = Date.now();
                console.log(`Command "${command.desc}" started at ${new Date(commandStartTime).toLocaleTimeString()}`);

                // Will store results from all IPs
                let results = [];

                // Process all IPs for the current command in parallel
                console.log(`\nExecuting "${command.desc}" on all IPs in parallel (2.2, 2.4, 2.6, ... 2.20):`);

                // First test connections to all IPs
                console.log("Testing connections to all IPs before executing command...");
                const connectionPromises = ips.map(ip => testConnection(ip, deviceUser, devicePassword));
                const connectionResults = await Promise.allSettled(connectionPromises);

                // Filter out unreachable IPs
                const reachableIps = ips.filter((_, index) =>
                    connectionResults[index].status === 'fulfilled' && connectionResults[index].value === true
                );

                if (reachableIps.length === 0) {
                    console.error("No IPs are reachable. Skipping command and moving to next one.");
                    hasErrors = true;
                    errorSummary.push(`Command "${command.desc}" skipped - No IPs reachable`);
                    continue;
                }

                console.log(`${reachableIps.length}/${ips.length} IPs are reachable. Proceeding with command execution.`);

                // Create promises for all reachable IPs to execute the command in parallel
                const promises = reachableIps.map(async (ip) => {
                    try {
                        console.log(`Starting on IP ${ip}: ${command.desc}`);
                        const result = await execCommand(command.cmd(ip), 15);
                        console.log(`Completed on IP ${ip}: ${command.desc} in ${result.executionTime.toFixed(2)}s`);
                        return { ip, success: true, executionTime: result.executionTime, output: result.output };
                    } catch (error) {
                        console.error(`Failed on IP ${ip}: ${command.desc} - ${error.message}`);
                        hasErrors = true;
                        return { ip, success: false, error: error.message, executionTime: 0 };
                    }
                });

                // Wait for all parallel executions to complete
                results = await Promise.all(promises);

                // Count successful executions
                const commandSuccessCount = results.filter(r => r.success).length;
                if (commandSuccessCount === reachableIps.length) {
                    successCount++;
                } else {
                    // Add error details to summary
                    const failedIps = results.filter(r => !r.success).map(r => r.ip);
                    errorSummary.push(`Command "${command.desc}" failed on IPs: ${failedIps.join(', ')}`);
                }

                // Wait a moment before moving to the next command
                await new Promise(resolve => setTimeout(resolve, 1000));
            }
        }

        if (selected.includes('operator') && selectedFirmwareFile && selectedDeviceType === 'operator') {
            console.log('Processing operator firmware...');

            // Use the actual selected firmware file, not a path in firmware directory
            const firmwareFileName = path.basename(selectedFirmwareFile);
            const targetFileName = 'svdgs_op'; // Standard name expected on the target system

            console.log('Selected firmware file:', selectedFirmwareFile);
            console.log('Firmware file name:', firmwareFileName);
            console.log('Target file name:', targetFileName);

            const operatorCommands = [
                {
                    cmd: (ip) => `sshpass -p '${devicePassword}' scp -o StrictHostKeyChecking=no "${selectedFirmwareFile}" ${deviceUser}@${ip}:~/${targetFileName}`,
                    desc: 'Copying operator firmware'
                },
                {
                    cmd: (ip) => `sshpass -p '${devicePassword}' ssh -o StrictHostKeyChecking=no ${deviceUser}@${ip} 'sshpass -p "${operatorPassword}" ssh -o StrictHostKeyChecking=no ${operatorUser}@*********** "echo \\"${operatorPassword}\\" | sudo -S systemctl stop opserver.service"'`,
                    desc: 'Stopping opserver.service'
                },
                {
                    cmd: (ip) => `sshpass -p '${devicePassword}' ssh -o StrictHostKeyChecking=no ${deviceUser}@${ip} 'sshpass -p "${operatorPassword}" scp -o StrictHostKeyChecking=no ${targetFileName} ${operatorUser}@***********:~/ && rm ${targetFileName}'`,
                    desc: 'Copying operator firmware to operator server'
                },
                {
                    cmd: (ip) => `sshpass -p '${devicePassword}' ssh -o StrictHostKeyChecking=no ${deviceUser}@${ip} 'sshpass -p "${operatorPassword}" ssh -o StrictHostKeyChecking=no ${operatorUser}@*********** "echo \\"${operatorPassword}\\" | sudo -S cp ${targetFileName} /mnt/SoftwareBackup/"'`,
                    desc: 'Backing up operator firmware'
                },
                {
                    cmd: (ip) => `sshpass -p '${devicePassword}' ssh -o StrictHostKeyChecking=no ${deviceUser}@${ip} 'sshpass -p "${operatorPassword}" ssh -o StrictHostKeyChecking=no ${operatorUser}@*********** "echo \\"${operatorPassword}\\" | sudo -S mv ${targetFileName} /root/"'`,
                    desc: 'Moving operator firmware'
                },
                {
                    cmd: (ip) => `sshpass -p '${devicePassword}' ssh -o StrictHostKeyChecking=no ${deviceUser}@${ip} 'sshpass -p "${operatorPassword}" ssh -o StrictHostKeyChecking=no ${operatorUser}@*********** "echo \\"${operatorPassword}\\" | sudo -S chmod +x /root/${targetFileName}"'`,
                    desc: 'Setting permissions'
                },
                {
                    cmd: (ip) => `sshpass -p '${devicePassword}' ssh -o StrictHostKeyChecking=no ${deviceUser}@${ip} 'sshpass -p "${operatorPassword}" ssh -o StrictHostKeyChecking=no ${operatorUser}@*********** "echo \\"${operatorPassword}\\" | sudo -S sha256sum /root/${targetFileName} > /root/${targetFileName}.sha256"'`,
                    desc: 'Generating SHA256'
                },
                {
                    cmd: (ip) => `sshpass -p '${devicePassword}' ssh -o StrictHostKeyChecking=no ${deviceUser}@${ip} 'sshpass -p "${operatorPassword}" ssh -o StrictHostKeyChecking=no ${operatorUser}@*********** "echo \\"${operatorPassword}\\" | sudo -S systemctl start opserver.service"'`,
                    desc: 'Starting opserver.service'
                }
            ];

            // Add debugging commands to check connectivity and file existence
            const debugCommands = [
                {
                    cmd: (ip) => `sshpass -p '${devicePassword}' ssh -o StrictHostKeyChecking=no ${deviceUser}@${ip} 'echo "Connection to smartdockai device successful"'`,
                    desc: 'Testing connection to smartdockai device'
                },
                {
                    cmd: (ip) => `sshpass -p '${devicePassword}' ssh -o StrictHostKeyChecking=no ${deviceUser}@${ip} 'sshpass -p "${operatorPassword}" ssh -o StrictHostKeyChecking=no ${operatorUser}@*********** "echo \\"Connection to operator server successful\\""'`,
                    desc: 'Testing connection to operator server'
                },
                {
                    cmd: () => `ls -la "${selectedFirmwareFile}"`,
                    desc: 'Checking firmware file exists locally',
                    local: true
                }
            ];

            // Execute debug commands first
            console.log('Running debug commands...');
            for (const command of debugCommands) {
                console.log(`Executing debug command: ${command.desc}`);

                // Handle local commands differently
                if (command.local) {
                    try {
                        console.log(`Local debug command: ${command.desc}`);
                        const result = await execCommand(command.cmd(), 10);
                        console.log(`Local debug success: ${command.desc}`);
                        console.log(`Output: ${result.output}`);
                    } catch (error) {
                        console.error(`Local debug failed: ${command.desc} - ${error.message}`);
                    }
                    continue;
                }

                // First test connections to all IPs
                const connectionPromises = ips.map(ip => testConnection(ip, deviceUser, devicePassword));
                const connectionResults = await Promise.allSettled(connectionPromises);

                // Filter out unreachable IPs
                const reachableIps = ips.filter((_, index) =>
                    connectionResults[index].status === 'fulfilled' && connectionResults[index].value === true
                );

                if (reachableIps.length === 0) {
                    console.error(`Debug command "${command.desc}" skipped - No IPs reachable`);
                    continue;
                }

                // Create promises for all reachable IPs to execute the debug command
                const promises = reachableIps.map(async (ip) => {
                    try {
                        console.log(`Debug on IP ${ip}: ${command.desc}`);
                        const result = await execCommand(command.cmd(ip), 10);
                        console.log(`Debug success on IP ${ip}: ${command.desc}`);
                        return { ip, success: true, output: result.output };
                    } catch (error) {
                        console.error(`Debug failed on IP ${ip}: ${command.desc} - ${error.message}`);
                        return { ip, success: false, error: error.message };
                    }
                });

                // Wait for all debug commands to complete
                const results = await Promise.allSettled(promises);
                console.log(`Debug command "${command.desc}" completed on ${results.length} IPs`);
            }

            // Execute each command on all IPs sequentially before moving to the next command
            console.log('Starting operator firmware deployment...');
            for (const command of operatorCommands) {
                totalCommands++;

                // Record command start time
                const commandStartTime = Date.now();
                console.log(`Command "${command.desc}" started at ${new Date(commandStartTime).toLocaleTimeString()}`);

                // Will store results from all IPs
                let results = [];

                // Process all IPs for the current command in parallel
                console.log(`\nExecuting "${command.desc}" on all IPs in parallel (2.2, 2.4, 2.6, ... 2.20):`);

                // First test connections to all IPs
                console.log("Testing connections to all IPs before executing command...");
                const connectionPromises = ips.map(ip => testConnection(ip, deviceUser, devicePassword));
                const connectionResults = await Promise.allSettled(connectionPromises);

                // Filter out unreachable IPs
                const reachableIps = ips.filter((_, index) =>
                    connectionResults[index].status === 'fulfilled' && connectionResults[index].value === true
                );

                if (reachableIps.length === 0) {
                    console.error("No IPs are reachable. Skipping command and moving to next one.");
                    hasErrors = true;
                    errorSummary.push(`Command "${command.desc}" skipped - No IPs reachable`);
                    continue;
                }

                console.log(`${reachableIps.length}/${ips.length} IPs are reachable. Proceeding with command execution.`);

                // Create promises for all reachable IPs to execute the command in parallel
                const promises = reachableIps.map(async (ip) => {
                    try {
                        console.log(`Starting on IP ${ip}: ${command.desc}`);
                        const result = await execCommand(command.cmd(ip), 15);
                        console.log(`Completed on IP ${ip}: ${command.desc} in ${result.executionTime.toFixed(2)}s`);
                        return { ip, success: true, executionTime: result.executionTime, output: result.output };
                    } catch (error) {
                        console.error(`Failed on IP ${ip}: ${command.desc} - ${error.message}`);
                        hasErrors = true;
                        return { ip, success: false, error: error.message, executionTime: 0 };
                    }
                });

                // Wait for all parallel executions to complete
                results = await Promise.all(promises);

                // Count successful executions
                const commandSuccessCount = results.filter(r => r.success).length;
                if (commandSuccessCount === reachableIps.length) {
                    successCount++;
                } else {
                    // Add error details to summary
                    const failedIps = results.filter(r => !r.success).map(r => r.ip);
                    errorSummary.push(`Command "${command.desc}" failed on IPs: ${failedIps.join(', ')}`);
                }

                // Wait a moment before moving to the next command
                await new Promise(resolve => setTimeout(resolve, 1000));
            }
        }

        // Generate completion message based on success/failure
        if (!hasErrors) {
            console.log('OTA activation completed successfully');
            return 'Update completed successfully';
        } else {
            const successRate = (successCount / totalCommands * 100).toFixed(1);
            const errorMessage = `OTA activation completed with errors (${successRate}% success rate)`;
            console.error(errorMessage);
            console.error('Error summary:');
            errorSummary.forEach(err => console.error(`- ${err}`));
            return {
                status: 'error',
                message: errorMessage,
                details: errorSummary
            };
        }
    } catch (error) {
        console.error('Error in activate-ota:', error);
        return {
            status: 'error',
            message: `Error: ${error.message}`,
            details: ['Unexpected error occurred during OTA activation']
        };
    }
});

ipcMain.handle('flash-firmware', async (event, selected) => {
    try {
        if (!selectedFirmwareFile || !fs.existsSync(selectedFirmwareFile)) {
            throw new Error('Firmware file not found');
        }

        // In a real implementation, you would need to detect connected devices
        // For now, we'll simulate a successful flash
        console.log(`Simulating firmware flash for ${selectedDeviceType} using ${selectedFirmwareFile}`);

        // Simulate a delay to make it seem like it's doing something
        await new Promise(resolve => setTimeout(resolve, 2000));

        return `Successfully flashed ${selectedDeviceType} firmware`;
    } catch (error) {
        console.error('Firmware flashing failed:', error);
        throw new Error(`Firmware flashing failed: ${error.message}`);
    }
});

// Handler for scanning devices in the network
ipcMain.handle('scan-devices', async (event, range) => {
    try {
        const subnet = range.subnet || '192.168.29'; // Default to 192.168.29 if not provided
        console.log(`Starting device scan in range: ${subnet}.${range.start} to ${subnet}.${range.end}`);

        const deviceUser = 'smartdockai';
        const devicePassword = 'Sach@151084!';
        const results = [];
        const baseIp = `${subnet}.`;

        // For progress reporting
        const totalIps = range.end - range.start + 1;
        let scannedCount = 0;

        // Scan IPs in batches to avoid overwhelming the system
        const batchSize = 10;

        for (let i = range.start; i <= range.end; i += batchSize) {
            const batchEnd = Math.min(i + batchSize - 1, range.end);
            const batchPromises = [];

            // Create a batch of promises
            for (let j = i; j <= batchEnd; j++) {
                const ip = `${baseIp}${j}`;

                batchPromises.push((async () => {
                    try {
                        // First check if IP is reachable - pass the event to send status updates
                        const isReachable = await testConnection(ip, deviceUser, devicePassword, 2, event);

                        if (isReachable) {
                            // If reachable, get hostname
                            const hostname = await getHostname(ip, deviceUser, devicePassword, 2);

                            // Check if hostname contains 'smartdockai' (case insensitive)
                            const isValidHostname = hostname && hostname.toLowerCase().includes('smartdockai');

                            // Send hostname status to renderer
                            event.sender.send('ip-scan-status', {
                                ip,
                                status: isValidHostname ? 'success' : 'warning',
                                message: isValidHostname ?
                                    `Valid device found (${hostname})` :
                                    `Device found but hostname is not valid (${hostname || 'Unknown'})`
                            });

                            return {
                                ip,
                                hostname: hostname || 'Unknown',
                                status: isValidHostname ? 'Valid' : 'Invalid hostname',
                                isValid: isValidHostname
                            };
                        }

                        return null; // IP not reachable
                    } catch (error) {
                        console.error(`Error scanning ${ip}:`, error);

                        // Send error status to renderer
                        event.sender.send('ip-scan-status', {
                            ip,
                            status: 'error',
                            message: `Error: ${error.message}`
                        });

                        return null;
                    } finally {
                        // Update progress
                        scannedCount++;
                        event.sender.send('scan-progress', {
                            current: scannedCount,
                            total: totalIps,
                            percentage: Math.floor((scannedCount / totalIps) * 100)
                        });
                    }
                })());
            }

            // Wait for the batch to complete
            const batchResults = await Promise.all(batchPromises);

            // Add valid results to the results array
            batchResults.forEach(result => {
                if (result) {
                    results.push(result);
                }
            });
        }

        console.log(`Scan completed. Found ${results.length} devices.`);
        return results;
    } catch (error) {
        console.error('Error during device scan:', error);
        throw new Error(`Device scan failed: ${error.message}`);
    }
});

// Handler for checking a single IP
ipcMain.handle('check-device', async (event, ip) => {
    try {
        console.log(`Checking device at IP: ${ip}`);

        const deviceUser = 'smartdockai';
        const devicePassword = 'Sach@151084!';

        // Check if IP is reachable - pass the event to send status updates
        const isReachable = await testConnection(ip, deviceUser, devicePassword, 3, event);

        if (!isReachable) {
            return {
                ip,
                hostname: 'Unknown',
                status: 'Not reachable',
                isValid: false
            };
        }

        // Get hostname
        const hostname = await getHostname(ip, deviceUser, devicePassword, 3);

        // Check if hostname contains 'smartdockai' (case insensitive)
        const isValidHostname = hostname && hostname.toLowerCase().includes('smartdockai');

        // Send hostname status to renderer
        event.sender.send('ip-scan-status', {
            ip,
            status: isValidHostname ? 'success' : 'warning',
            message: isValidHostname ?
                `Valid device found (${hostname})` :
                `Device found but hostname is not valid (${hostname || 'Unknown'})`
        });

        return {
            ip,
            hostname: hostname || 'Unknown',
            status: isValidHostname ? 'Valid' : 'Invalid hostname',
            isValid: isValidHostname
        };
    } catch (error) {
        console.error(`Error checking device at ${ip}:`, error);

        // Send error status to renderer
        event.sender.send('ip-scan-status', {
            ip,
            status: 'error',
            message: `Error: ${error.message}`
        });

        throw new Error(`Device check failed: ${error.message}`);
    }
});